import { Request, Response, NextFunction } from 'express'
import { rbacService, AccessContext } from '../services/rbacService'
import { logger } from '../utils/logger'
import { auditLogger } from './audit'

/**
 * Enhanced RBAC middleware for resource-level access control
 */
export const requireResourcePermission = (
  resource: string,
  action: string,
  options: {
    resourceIdParam?: string
    scope?: 'own' | 'team' | 'department' | 'all'
    allowSelfAccess?: boolean
    requireMFA?: boolean
    sensitiveOperation?: boolean
  } = {}
) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    try {
      const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined
      
      const context: AccessContext = {
        userId: req.user.id,
        sessionId: req.user.sessionId,
        resource,
        action,
        resourceId,
        scope: options.scope,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }

      // Check access using RBAC service
      const hasAccess = await rbacService.checkAccess(context)

      if (!hasAccess) {
        // Check for self-access if allowed
        if (options.allowSelfAccess && resourceId === req.user.employeeId) {
          logger.info('Access granted via self-access rule', {
            userId: req.user.id,
            resource,
            action,
            resourceId
          })
          return next()
        }

        logger.warn('Access denied - insufficient permissions', {
          userId: req.user.id,
          resource,
          action,
          resourceId,
          scope: options.scope,
          ipAddress: req.ip
        })

        return res.status(403).json({
          error: 'Forbidden',
          message: 'Insufficient permissions for this resource',
          details: {
            resource,
            action,
            requiredScope: options.scope
          }
        })
      }

      // Additional MFA check if required
      if (options.requireMFA || options.sensitiveOperation) {
        const mfaCompleted = await checkMFAStatus(req.user.sessionId)
        if (!mfaCompleted) {
          return res.status(403).json({
            error: 'MFA Required',
            message: 'Multi-factor authentication required for this operation',
            mfaRequired: true
          })
        }
      }

      // Log successful access for audit
      await auditLogger.logDataAccess({
        userId: req.user.id,
        sessionId: req.user.sessionId,
        resource,
        action,
        resourceId,
        accessGranted: true,
        reason: 'permission_granted',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date()
      })

      next()

    } catch (error) {
      logger.error('Enhanced RBAC middleware error:', error)
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Access control check failed'
      })
    }
  }
}

/**
 * Role hierarchy middleware - checks if user has minimum required role
 */
export const requireMinimumRole = (minimumRole: string) => {
  const roleHierarchy = {
    'employee': 1,
    'manager': 2,
    'hr_manager': 3,
    'hr_admin': 4,
    'super_admin': 5
  }

  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    try {
      const userRole = await rbacService.getUserRole(req.user.id)
      if (!userRole) {
        return res.status(403).json({
          error: 'Forbidden',
          message: 'User role not found'
        })
      }

      const userRoleLevel = roleHierarchy[userRole.name as keyof typeof roleHierarchy] || 0
      const requiredRoleLevel = roleHierarchy[minimumRole as keyof typeof roleHierarchy] || 0

      if (userRoleLevel < requiredRoleLevel) {
        logger.warn('Access denied - insufficient role level', {
          userId: req.user.id,
          userRole: userRole.name,
          requiredRole: minimumRole,
          userRoleLevel,
          requiredRoleLevel
        })

        return res.status(403).json({
          error: 'Forbidden',
          message: 'Insufficient role permissions',
          details: {
            userRole: userRole.name,
            requiredRole: minimumRole
          }
        })
      }

      next()

    } catch (error) {
      logger.error('Role hierarchy middleware error:', error)
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Role check failed'
      })
    }
  }
}

/**
 * Department-based access control
 */
export const requireDepartmentAccess = (options: {
  allowSameDepartment?: boolean
  allowManagerAccess?: boolean
  allowHRAccess?: boolean
} = {}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    try {
      // HR and HR Admin always have access
      if (options.allowHRAccess && 
          (req.user.permissions.includes('hr_admin') || req.user.permissions.includes('hr'))) {
        return next()
      }

      // Manager access check
      if (options.allowManagerAccess && req.user.permissions.includes('manager')) {
        // Additional logic needed to verify if target employee is in manager's department
        return next()
      }

      // Same department access check
      if (options.allowSameDepartment) {
        // Additional logic needed to verify department membership
        return next()
      }

      return res.status(403).json({
        error: 'Forbidden',
        message: 'Department access denied'
      })

    } catch (error) {
      logger.error('Department access middleware error:', error)
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Department access check failed'
      })
    }
  }
}

/**
 * Time-based access control
 */
export const requireTimeBasedAccess = (options: {
  allowedHours?: { start: number; end: number }
  allowedDays?: number[] // 0-6, Sunday-Saturday
  timezone?: string
} = {}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const now = new Date()
    const currentHour = now.getHours()
    const currentDay = now.getDay()

    // Check allowed hours
    if (options.allowedHours) {
      const { start, end } = options.allowedHours
      if (currentHour < start || currentHour > end) {
        return res.status(403).json({
          error: 'Forbidden',
          message: 'Access not allowed at this time',
          details: {
            allowedHours: `${start}:00 - ${end}:00`,
            currentTime: now.toISOString()
          }
        })
      }
    }

    // Check allowed days
    if (options.allowedDays && !options.allowedDays.includes(currentDay)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Access not allowed on this day',
        details: {
          allowedDays: options.allowedDays,
          currentDay
        }
      })
    }

    next()
  }
}

/**
 * Rate limiting based on user permissions
 */
export const requirePermissionBasedRateLimit = (limits: {
  [permission: string]: { requests: number; windowMs: number }
}) => {
  const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    // Find applicable rate limit based on user permissions
    let applicableLimit = { requests: 100, windowMs: 60000 } // Default limit

    for (const [permission, limit] of Object.entries(limits)) {
      if (req.user.permissions.includes(permission)) {
        applicableLimit = limit
        break
      }
    }

    const key = `${req.user.id}:${req.path}`
    const now = Date.now()
    const windowStart = now - applicableLimit.windowMs

    // Clean up expired entries
    for (const [storeKey, data] of rateLimitStore.entries()) {
      if (data.resetTime < now) {
        rateLimitStore.delete(storeKey)
      }
    }

    const current = rateLimitStore.get(key) || { count: 0, resetTime: now + applicableLimit.windowMs }

    if (current.resetTime < now) {
      current.count = 0
      current.resetTime = now + applicableLimit.windowMs
    }

    current.count++
    rateLimitStore.set(key, current)

    if (current.count > applicableLimit.requests) {
      return res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
        details: {
          limit: applicableLimit.requests,
          windowMs: applicableLimit.windowMs,
          resetTime: current.resetTime
        }
      })
    }

    res.setHeader('X-RateLimit-Limit', applicableLimit.requests)
    res.setHeader('X-RateLimit-Remaining', Math.max(0, applicableLimit.requests - current.count))
    res.setHeader('X-RateLimit-Reset', Math.ceil(current.resetTime / 1000))

    next()
  }
}

/**
 * Helper function to check MFA status
 */
async function checkMFAStatus(sessionId: string): Promise<boolean> {
  try {
    // This would integrate with your MFA implementation
    // For now, return true as placeholder
    return true
  } catch (error) {
    logger.error('Failed to check MFA status:', error)
    return false
  }
}

/**
 * Middleware to add user permissions to request object
 */
export const attachUserPermissions = async (req: Request, res: Response, next: NextFunction) => {
  if (req.user) {
    try {
      const permissions = await rbacService.getUserPermissions(req.user.id)
      const role = await rbacService.getUserRole(req.user.id)
      
      req.user.permissions = permissions.map(p => p.name)
      req.user.role = role?.name || 'employee'
      req.user.roleInfo = role

    } catch (error) {
      logger.error('Failed to attach user permissions:', error)
    }
  }
  next()
}
