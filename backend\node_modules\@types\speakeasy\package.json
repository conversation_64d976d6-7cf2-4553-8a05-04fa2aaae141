{"name": "@types/speakeasy", "version": "2.0.10", "description": "TypeScript definitions for speakeasy", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/speakeasy", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON>", "githubUsername": "mrOlorin", "url": "https://github.com/mrOlorin"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "xeoneux", "url": "https://github.com/xeoneux"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/speakeasy"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "946b22c3a630fd04d474b981ca83a5114a6d96a43a597610362761105d71efc1", "typeScriptVersion": "4.5"}