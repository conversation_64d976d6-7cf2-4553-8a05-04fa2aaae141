-- Enhanced Audit & Monitoring System Migration
-- This migration creates comprehensive audit logging, security monitoring,
-- and GDPR-compliant data access tracking capabilities

-- Create audit events table for comprehensive event logging
CREATE TABLE IF NOT EXISTS audit_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    
    -- Event classification
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(50) NOT NULL CHECK (
        event_category IN ('authentication', 'authorization', 'data_access', 
                          'data_modification', 'administrative_action', 
                          'security_action', 'system_event')
    ),
    
    -- Resource information
    resource VARCHAR(100),
    resource_id VARCHAR(255),
    action VARCHAR(50),
    
    -- Event details and context
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    
    -- Event metadata
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    severity VARCHAR(20) DEFAULT 'medium' CHECK (
        severity IN ('low', 'medium', 'high', 'critical')
    ),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    risk_score INTEGER DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
    
    -- Compliance tracking
    compliance_flags TEXT[] DEFAULT '{}',
    retention_until DATE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit events
CREATE INDEX IF NOT EXISTS idx_audit_events_user_id ON audit_events(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_events_timestamp ON audit_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_events_event_type ON audit_events(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_events_event_category ON audit_events(event_category);
CREATE INDEX IF NOT EXISTS idx_audit_events_severity ON audit_events(severity);
CREATE INDEX IF NOT EXISTS idx_audit_events_resource ON audit_events(resource, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_events_ip_address ON audit_events(ip_address);
CREATE INDEX IF NOT EXISTS idx_audit_events_compliance ON audit_events USING GIN(compliance_flags);
CREATE INDEX IF NOT EXISTS idx_audit_events_details ON audit_events USING GIN(details);

-- Create security events table for specialized security monitoring
CREATE TABLE IF NOT EXISTS security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    
    -- Security event details
    event_type VARCHAR(100) NOT NULL,
    details JSONB NOT NULL DEFAULT '{}',
    
    -- Context information
    ip_address INET NOT NULL,
    user_agent TEXT,
    geolocation JSONB, -- {country, region, city, lat, lng}
    
    -- Risk assessment
    severity VARCHAR(20) DEFAULT 'medium' CHECK (
        severity IN ('low', 'medium', 'high', 'critical')
    ),
    risk_score INTEGER DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
    threat_indicators TEXT[],
    
    -- Investigation status
    investigated BOOLEAN DEFAULT false,
    investigation_notes TEXT,
    false_positive BOOLEAN DEFAULT false,
    
    -- Timestamps
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for security events
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_risk_score ON security_events(risk_score);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON security_events(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_events_investigated ON security_events(investigated);
CREATE INDEX IF NOT EXISTS idx_security_events_details ON security_events USING GIN(details);

-- Create data access log for GDPR compliance
CREATE TABLE IF NOT EXISTS data_access_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    
    -- Data access details
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255),
    action VARCHAR(50) NOT NULL CHECK (
        action IN ('read', 'create', 'update', 'delete', 'export', 'print')
    ),
    
    -- Data classification and scope
    data_classification VARCHAR(20) NOT NULL CHECK (
        data_classification IN ('public', 'internal', 'confidential', 'restricted')
    ),
    record_count INTEGER DEFAULT 1,
    fields_accessed TEXT[],
    
    -- GDPR compliance fields
    purpose TEXT,
    legal_basis VARCHAR(100), -- consent, contract, legal_obligation, etc.
    data_subject_id UUID, -- ID of the person whose data was accessed
    
    -- Context information
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    retention_until DATE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for data access log
CREATE INDEX IF NOT EXISTS idx_data_access_user_id ON data_access_log(user_id);
CREATE INDEX IF NOT EXISTS idx_data_access_timestamp ON data_access_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_data_access_resource ON data_access_log(resource, resource_id);
CREATE INDEX IF NOT EXISTS idx_data_access_action ON data_access_log(action);
CREATE INDEX IF NOT EXISTS idx_data_access_classification ON data_access_log(data_classification);
CREATE INDEX IF NOT EXISTS idx_data_access_subject ON data_access_log(data_subject_id);
CREATE INDEX IF NOT EXISTS idx_data_access_legal_basis ON data_access_log(legal_basis);

-- Create suspicious activity patterns table
CREATE TABLE IF NOT EXISTS suspicious_activity_patterns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pattern_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Pattern definition
    pattern_type VARCHAR(50) NOT NULL CHECK (
        pattern_type IN ('frequency', 'location', 'time', 'behavior', 'data_volume')
    ),
    pattern_rules JSONB NOT NULL,
    
    -- Thresholds and scoring
    risk_score INTEGER DEFAULT 50 CHECK (risk_score >= 0 AND risk_score <= 100),
    threshold_value DECIMAL(10,2),
    time_window_minutes INTEGER,
    
    -- Pattern status
    is_active BOOLEAN DEFAULT true,
    auto_block BOOLEAN DEFAULT false,
    alert_level VARCHAR(20) DEFAULT 'medium' CHECK (
        alert_level IN ('low', 'medium', 'high', 'critical')
    ),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Insert default suspicious activity patterns
INSERT INTO suspicious_activity_patterns (
    pattern_name, description, pattern_type, pattern_rules, risk_score, 
    threshold_value, time_window_minutes, alert_level
) VALUES
('Multiple Failed Logins', 'Multiple failed login attempts from same IP', 'frequency', 
 '{"event_type": "login_failed", "count_threshold": 5}', 70, 5, 15, 'high'),
('Unusual Login Location', 'Login from unusual geographic location', 'location',
 '{"event_type": "login_success", "location_variance": "high"}', 60, 1, 60, 'medium'),
('Off-Hours Data Access', 'Data access outside normal business hours', 'time',
 '{"event_type": "data_access", "time_range": "off_hours"}', 50, 1, 60, 'medium'),
('Bulk Data Export', 'Large volume data export activity', 'data_volume',
 '{"event_type": "data_export", "record_threshold": 1000}', 80, 1000, 60, 'high'),
('Privilege Escalation Attempt', 'Attempts to access unauthorized resources', 'behavior',
 '{"event_type": "authorization_failed", "resource_sensitivity": "high"}', 90, 3, 30, 'critical')
ON CONFLICT (pattern_name) DO NOTHING;

-- Create activity monitoring alerts table
CREATE TABLE IF NOT EXISTS activity_monitoring_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pattern_id UUID NOT NULL REFERENCES suspicious_activity_patterns(id),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    
    -- Alert details
    alert_type VARCHAR(100) NOT NULL,
    alert_message TEXT NOT NULL,
    risk_score INTEGER NOT NULL,
    severity VARCHAR(20) NOT NULL,
    
    -- Triggering events
    triggering_events UUID[] DEFAULT '{}',
    event_count INTEGER DEFAULT 1,
    time_window_start TIMESTAMP WITH TIME ZONE,
    time_window_end TIMESTAMP WITH TIME ZONE,
    
    -- Context information
    ip_address INET,
    user_agent TEXT,
    additional_context JSONB DEFAULT '{}',
    
    -- Alert status
    status VARCHAR(20) DEFAULT 'open' CHECK (
        status IN ('open', 'investigating', 'resolved', 'false_positive')
    ),
    assigned_to UUID REFERENCES users(id),
    resolution_notes TEXT,
    
    -- Actions taken
    auto_actions_taken TEXT[],
    manual_actions_taken TEXT[],
    
    -- Timestamps
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for monitoring alerts
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_pattern ON activity_monitoring_alerts(pattern_id);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_user ON activity_monitoring_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_triggered ON activity_monitoring_alerts(triggered_at);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_status ON activity_monitoring_alerts(status);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_severity ON activity_monitoring_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_risk_score ON activity_monitoring_alerts(risk_score);

-- Create compliance reports table
CREATE TABLE IF NOT EXISTS compliance_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type VARCHAR(50) NOT NULL CHECK (
        report_type IN ('gdpr_access', 'data_retention', 'security_incidents', 'user_activity')
    ),
    report_name VARCHAR(200) NOT NULL,
    
    -- Report parameters
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    filters JSONB DEFAULT '{}',
    
    -- Report content
    report_data JSONB,
    summary JSONB,
    total_records INTEGER,
    
    -- Report metadata
    generated_by UUID NOT NULL REFERENCES users(id),
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    file_path VARCHAR(500), -- Path to exported file if applicable
    file_format VARCHAR(20), -- json, csv, pdf
    
    -- Access control
    access_level VARCHAR(20) DEFAULT 'restricted' CHECK (
        access_level IN ('public', 'internal', 'restricted', 'confidential')
    ),
    retention_until DATE,
    
    -- Status
    status VARCHAR(20) DEFAULT 'completed' CHECK (
        status IN ('generating', 'completed', 'failed', 'archived')
    ),
    error_message TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for compliance reports
CREATE INDEX IF NOT EXISTS idx_compliance_reports_type ON compliance_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_generated_by ON compliance_reports(generated_by);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_generated_at ON compliance_reports(generated_at);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_status ON compliance_reports(status);

-- Create function to automatically set retention dates
CREATE OR REPLACE FUNCTION set_audit_retention()
RETURNS TRIGGER AS $$
BEGIN
    -- Set retention date based on data classification and compliance requirements
    IF NEW.compliance_flags && ARRAY['gdpr', 'data_protection'] THEN
        NEW.retention_until := (NEW.created_at + INTERVAL '7 years')::DATE;
    ELSIF NEW.severity IN ('high', 'critical') THEN
        NEW.retention_until := (NEW.created_at + INTERVAL '10 years')::DATE;
    ELSE
        NEW.retention_until := (NEW.created_at + INTERVAL '3 years')::DATE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic retention date setting
DROP TRIGGER IF EXISTS trigger_set_audit_retention ON audit_events;
CREATE TRIGGER trigger_set_audit_retention
    BEFORE INSERT ON audit_events
    FOR EACH ROW
    EXECUTE FUNCTION set_audit_retention();

DROP TRIGGER IF EXISTS trigger_set_data_access_retention ON data_access_log;
CREATE TRIGGER trigger_set_data_access_retention
    BEFORE INSERT ON data_access_log
    FOR EACH ROW
    EXECUTE FUNCTION set_audit_retention();

-- Create function for audit data cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_audit_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up expired audit events
    DELETE FROM audit_events WHERE retention_until < CURRENT_DATE;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up expired security events (default 7 years)
    DELETE FROM security_events WHERE created_at < NOW() - INTERVAL '7 years';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up expired data access logs
    DELETE FROM data_access_log WHERE retention_until < CURRENT_DATE;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up old resolved alerts (keep for 2 years)
    DELETE FROM activity_monitoring_alerts 
    WHERE status IN ('resolved', 'false_positive') 
    AND resolved_at < NOW() - INTERVAL '2 years';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create views for monitoring and reporting
CREATE OR REPLACE VIEW audit_summary AS
SELECT 
    DATE_TRUNC('day', timestamp) as date,
    event_category,
    severity,
    COUNT(*) as event_count,
    COUNT(*) FILTER (WHERE success = false) as failed_events,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT ip_address) as unique_ips,
    AVG(risk_score) as avg_risk_score
FROM audit_events
WHERE timestamp >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', timestamp), event_category, severity
ORDER BY date DESC, event_category, severity;

CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    DATE_TRUNC('hour', timestamp) as hour,
    event_type,
    severity,
    COUNT(*) as incident_count,
    MAX(risk_score) as max_risk_score,
    COUNT(DISTINCT user_id) as affected_users,
    COUNT(DISTINCT ip_address) as source_ips
FROM security_events
WHERE timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', timestamp), event_type, severity
ORDER BY hour DESC, max_risk_score DESC;

-- Add comments for documentation
COMMENT ON TABLE audit_events IS 'Comprehensive audit log for all system events';
COMMENT ON TABLE security_events IS 'Specialized security incident tracking';
COMMENT ON TABLE data_access_log IS 'GDPR-compliant data access logging';
COMMENT ON TABLE suspicious_activity_patterns IS 'Configurable patterns for detecting suspicious activity';
COMMENT ON TABLE activity_monitoring_alerts IS 'Alerts generated from suspicious activity detection';
COMMENT ON TABLE compliance_reports IS 'Generated compliance and audit reports';
COMMENT ON FUNCTION cleanup_expired_audit_data() IS 'Cleanup function for expired audit data based on retention policies';
COMMENT ON VIEW audit_summary IS 'Daily summary of audit events for monitoring dashboard';
COMMENT ON VIEW security_dashboard IS 'Real-time security incident dashboard view';
