import express from 'express'
import dotenv from 'dotenv'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import session from 'express-session'

// Import routes
import authRoutes from './routes/auth'
import mfaRoutes from './routes/mfa'
import auditRoutes from './routes/audit'
import employeeRoutes from './routes/employees'
import departmentRoutes from './routes/departments'
import positionRoutes from './routes/positions'
import onboardingRoutes from './routes/onboarding'
// import offboardingRoutes from './routes/offboarding'
// import documentsRoutes from './routes/documents'
// import performanceRoutes from './routes/performance'
import aiRoutes from './routes/ai'
// import securityRoutes from './routes/security'

// Import middleware
import { errorHandler } from './middleware/errorHandler'
import { authMiddleware } from './middleware/auth'
import { logger, morganStream } from './utils/logger'
import securityMiddleware from './middleware/security'
import { auditMiddleware } from './utils/auditLogger'

// Load environment variables
dotenv.config()

console.log('🚀 Starting PeopleNest Backend Server...')
console.log('Environment variables loaded')

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

const app = express()
const PORT = process.env.PORT || 3001

console.log(`Server will run on port: ${PORT}`)

// Basic middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1)

// Session configuration for security tracking
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 8 * 60 * 60 * 1000, // 8 hours
    sameSite: 'strict'
  },
  name: 'peoplenest.sid' // Custom session name
}))

// Enhanced security headers
app.use(securityMiddleware.securityHeaders)

// Input sanitization
app.use(securityMiddleware.sanitizeInput)

// Rate limiting for different endpoints
app.use('/api/auth', securityMiddleware.authRateLimit)
app.use('/api/', securityMiddleware.apiRateLimit)

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID'],
  exposedHeaders: ['X-Request-ID'],
  maxAge: 86400 // 24 hours
}))

// Compression middleware
app.use(compression())

// Logging middleware with audit integration
app.use(morgan('combined', { stream: morganStream }))

// Session security middleware
app.use(securityMiddleware.sessionSecurity)

// Audit middleware for all requests
app.use(auditMiddleware)

// Suspicious activity detection
app.use(securityMiddleware.detectSuspiciousActivity)

// Data loss prevention
app.use(securityMiddleware.dataLossPrevention)

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/mfa', mfaRoutes)
app.use('/api/audit', auditRoutes)
app.use('/api/employees', employeeRoutes)
app.use('/api/departments', departmentRoutes)
app.use('/api/positions', positionRoutes)
app.use('/api/onboarding', onboardingRoutes)
// app.use('/api/offboarding', authMiddleware, securityMiddleware.protectPII, offboardingRoutes)
// app.use('/api/documents', authMiddleware, documentsRoutes)
// app.use('/api/performance', authMiddleware, performanceRoutes)
app.use('/api/ai', aiRoutes)
// app.use('/api/security', securityRoutes)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  })
})

// Global error handler
app.use(errorHandler)

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully')
  process.exit(0)
})

// Start server
try {
  const server = app.listen(PORT, () => {
    console.log(`🚀 PeopleNest Backend API server running on port ${PORT}`)
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`)
    console.log(`🔗 Health check: http://localhost:${PORT}/health`)
  })

  server.on('error', (error) => {
    console.error('Server error:', error)
  })
} catch (error) {
  console.error('Failed to start server:', error)
  process.exit(1)
}

export default app
