import { MFAService } from '../../services/mfaService'
import { DatabaseService } from '../../services/databaseService'
import * as speakeasy from 'speakeasy'
import * as QRCode from 'qrcode'

// Mock dependencies
jest.mock('../../services/databaseService')
jest.mock('../../utils/logger')
jest.mock('speakeasy')
jest.mock('qrcode')

const mockDb = {
  query: jest.fn()
}

// Mock DatabaseService constructor
;(DatabaseService as jest.MockedClass<typeof DatabaseService>).mockImplementation(() => mockDb as any)

// Mock speakeasy and QRCode
jest.mock('speakeasy', () => ({
  generateSecret: jest.fn(),
  totp: {
    verify: jest.fn()
  }
}))

jest.mock('qrcode', () => ({
  toDataURL: jest.fn()
}))

const mockSpeakeasy = {
  generateSecret: speakeasy.generateSecret as jest.MockedFunction<typeof speakeasy.generateSecret>,
  totp: {
    verify: speakeasy.totp.verify as jest.MockedFunction<typeof speakeasy.totp.verify>
  }
}

const mockQRCode = QRCode as jest.Mocked<typeof QRCode>

describe('MFAService', () => {
  let mfaService: MFAService
  
  beforeEach(() => {
    mfaService = new MFAService()
    jest.clearAllMocks()
  })

  describe('generateMFASecret', () => {
    it('should generate MFA secret and QR code successfully', async () => {
      const userId = 'user-123'
      const userEmail = '<EMAIL>'
      const mockSecret = 'JBSWY3DPEHPK3PXP'
      const mockQRCodeUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'

      // Mock speakeasy.generateSecret
      mockSpeakeasy.generateSecret.mockReturnValue({
        ascii: 'test',
        hex: 'test',
        base32: mockSecret,
        google_auth_qr: 'otpauth://totp/PeopleNest:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=PeopleNest'
      } as any)

      // Mock QRCode.toDataURL
      ;(mockQRCode.toDataURL as jest.Mock).mockResolvedValue(mockQRCodeUrl)

      // Mock database operations
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Insert setup
      mockDb.query.mockResolvedValueOnce({ rows: [{ codes: ['12345678', '87654321'] }] }) // Generate backup codes

      const result = await mfaService.generateMFASecret(userId, userEmail)

      expect(result).toEqual({
        secret: mockSecret,
        qrCodeUrl: mockQRCodeUrl,
        backupCodes: ['12345678', '87654321']
      })

      expect(mockSpeakeasy.generateSecret).toHaveBeenCalledWith({
        name: userEmail,
        issuer: 'PeopleNest',
        length: 32
      })

      expect(mockQRCode.toDataURL).toHaveBeenCalled()
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO user_mfa_setup'),
        expect.arrayContaining([userId, mockSecret])
      )
    })

    it('should handle database errors', async () => {
      const userId = 'user-123'
      const userEmail = '<EMAIL>'

      mockSpeakeasy.generateSecret.mockReturnValue({
        base32: 'JBSWY3DPEHPK3PXP'
      } as any)

      ;(mockQRCode.toDataURL as jest.Mock).mockResolvedValue('qr-code-url')
      mockDb.query.mockRejectedValueOnce(new Error('Database error'))

      await expect(mfaService.generateMFASecret(userId, userEmail))
        .rejects.toThrow('Database error')
    })
  })

  describe('verifyAndEnableMFA', () => {
    it('should verify token and enable MFA successfully', async () => {
      const userId = 'user-123'
      const token = '123456'
      const sessionId = 'session-456'

      // Mock getting setup data
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          secret: 'JBSWY3DPEHPK3PXP',
          backup_codes: ['12345678', '87654321']
        }]
      })

      // Mock token verification
      mockSpeakeasy.totp.verify.mockReturnValue(true)

      // Mock MFA enablement
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Insert user_mfa
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Delete setup
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Update session

      const result = await mfaService.verifyAndEnableMFA(userId, token, sessionId)

      expect(result).toBe(true)
      expect(mockSpeakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'JBSWY3DPEHPK3PXP',
        encoding: 'base32',
        token,
        window: 2
      })
    })

    it('should reject invalid token', async () => {
      const userId = 'user-123'
      const token = '000000'

      // Mock getting setup data
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          secret: 'JBSWY3DPEHPK3PXP',
          backup_codes: ['12345678', '87654321']
        }]
      })

      // Mock token verification failure
      mockSpeakeasy.totp.verify.mockReturnValue(false)

      const result = await mfaService.verifyAndEnableMFA(userId, token)

      expect(result).toBe(false)
    })

    it('should handle missing setup data', async () => {
      const userId = 'user-123'
      const token = '123456'

      // Mock no setup data found
      mockDb.query.mockResolvedValueOnce({ rows: [] })

      await expect(mfaService.verifyAndEnableMFA(userId, token))
        .rejects.toThrow('MFA setup not found or expired')
    })
  })

  describe('verifyMFAToken', () => {
    it('should verify TOTP token successfully', async () => {
      const userId = 'user-123'
      const token = '123456'
      const sessionId = 'session-456'

      // Mock getting MFA config
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          secret: 'JBSWY3DPEHPK3PXP',
          backup_codes: ['12345678', '87654321'],
          is_active: true
        }]
      })

      // Mock token verification
      mockSpeakeasy.totp.verify.mockReturnValue(true)

      // Mock logging verification attempt
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })
      // Mock updating session
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })

      const result = await mfaService.verifyMFAToken(userId, token, sessionId)

      expect(result).toEqual({
        success: true,
        method: 'totp'
      })
    })

    it('should verify backup code successfully', async () => {
      const userId = 'user-123'
      const backupCode = '12345678'
      const sessionId = 'session-456'

      // Mock getting MFA config
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          secret: 'JBSWY3DPEHPK3PXP',
          backup_codes: ['12345678', '87654321'],
          is_active: true
        }]
      })

      // Mock TOTP verification failure (so it tries backup code)
      mockSpeakeasy.totp.verify.mockReturnValue(false)

      // Mock backup code usage
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Log attempt
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Remove used backup code
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Update session

      const result = await mfaService.verifyMFAToken(userId, backupCode, sessionId)

      expect(result).toEqual({
        success: true,
        method: 'backup_code'
      })
    })

    it('should reject invalid token and backup code', async () => {
      const userId = 'user-123'
      const token = '000000'

      // Mock getting MFA config
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          secret: 'JBSWY3DPEHPK3PXP',
          backup_codes: ['12345678', '87654321'],
          is_active: true
        }]
      })

      // Mock TOTP verification failure
      mockSpeakeasy.totp.verify.mockReturnValue(false)

      // Mock logging verification attempt
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })

      const result = await mfaService.verifyMFAToken(userId, token)

      expect(result).toEqual({
        success: false,
        method: null
      })
    })

    it('should handle inactive MFA', async () => {
      const userId = 'user-123'
      const token = '123456'

      // Mock getting inactive MFA config
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          secret: 'JBSWY3DPEHPK3PXP',
          backup_codes: ['12345678', '87654321'],
          is_active: false
        }]
      })

      await expect(mfaService.verifyMFAToken(userId, token))
        .rejects.toThrow('MFA is not active for this user')
    })
  })

  describe('getMFAStatus', () => {
    it('should return MFA status for user with active MFA', async () => {
      const userId = 'user-123'

      mockDb.query.mockResolvedValueOnce({
        rows: [{
          is_active: true,
          backup_codes_count: 8,
          created_at: new Date('2023-01-01'),
          last_used: new Date('2023-01-15')
        }]
      })

      const result = await mfaService.getMFAStatus(userId)

      expect(result).toEqual({
        isEnabled: true,
        backupCodesRemaining: 8,
        setupDate: new Date('2023-01-01'),
        lastUsed: new Date('2023-01-15')
      })
    })

    it('should return disabled status for user without MFA', async () => {
      const userId = 'user-123'

      mockDb.query.mockResolvedValueOnce({ rows: [] })

      const result = await mfaService.getMFAStatus(userId)

      expect(result).toEqual({
        isEnabled: false,
        backupCodesRemaining: 0,
        setupDate: null,
        lastUsed: null
      })
    })
  })

  describe('disableMFA', () => {
    it('should disable MFA successfully', async () => {
      const userId = 'user-123'
      const adminUserId = 'admin-456'

      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Delete user_mfa
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 }) // Update sessions

      const result = await mfaService.disableMFA(userId, adminUserId)

      expect(result).toBe(true)
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM user_mfa'),
        [userId]
      )
    })
  })

  describe('regenerateBackupCodes', () => {
    it('should regenerate backup codes successfully', async () => {
      const userId = 'user-123'

      // Mock generating new backup codes
      mockDb.query.mockResolvedValueOnce({
        rows: [{ codes: ['11111111', '22222222', '33333333'] }]
      })

      // Mock updating user_mfa
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })

      const result = await mfaService.regenerateBackupCodes(userId)

      expect(result).toEqual(['11111111', '22222222', '33333333'])
    })
  })

  describe('isMFARequired', () => {
    it('should return true for users requiring MFA', async () => {
      const userId = 'user-123'

      mockDb.query.mockResolvedValueOnce({
        rows: [{ is_mfa_required: true }]
      })

      const result = await mfaService.isMFARequired(userId)

      expect(result).toBe(true)
    })

    it('should return false for users not requiring MFA', async () => {
      const userId = 'user-123'

      mockDb.query.mockResolvedValueOnce({
        rows: [{ is_mfa_required: false }]
      })

      const result = await mfaService.isMFARequired(userId)

      expect(result).toBe(false)
    })

    it('should handle database errors', async () => {
      const userId = 'user-123'

      mockDb.query.mockRejectedValueOnce(new Error('Database error'))

      await expect(mfaService.isMFARequired(userId))
        .rejects.toThrow('Database error')
    })
  })
})
