#!/usr/bin/env ts-node

import { spawn } from 'child_process'
import * as path from 'path'
import * as fs from 'fs'

interface TestSuite {
  name: string
  description: string
  command: string
  args: string[]
  timeout?: number
}

interface TestResults {
  suite: string
  passed: boolean
  duration: number
  coverage?: number
  errors?: string[]
}

class TestRunner {
  private results: TestResults[] = []
  private startTime: number = 0

  constructor() {
    this.startTime = Date.now()
  }

  async runTestSuite(suite: TestSuite): Promise<TestResults> {
    console.log(`\n🧪 Running ${suite.name}...`)
    console.log(`📝 ${suite.description}`)
    
    const startTime = Date.now()
    
    return new Promise((resolve) => {
      const child = spawn(suite.command, suite.args, {
        stdio: 'pipe',
        shell: true,
        cwd: process.cwd()
      })

      let stdout = ''
      let stderr = ''

      child.stdout?.on('data', (data) => {
        stdout += data.toString()
        process.stdout.write(data)
      })

      child.stderr?.on('data', (data) => {
        stderr += data.toString()
        process.stderr.write(data)
      })

      const timeout = setTimeout(() => {
        child.kill('SIGTERM')
        console.log(`⏰ Test suite ${suite.name} timed out`)
      }, suite.timeout || 300000) // 5 minutes default

      child.on('close', (code) => {
        clearTimeout(timeout)
        const duration = Date.now() - startTime
        const passed = code === 0

        // Extract coverage percentage if available
        let coverage: number | undefined
        const coverageMatch = stdout.match(/All files[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*(\d+\.?\d*)/);
        if (coverageMatch) {
          coverage = parseFloat(coverageMatch[1])
        }

        const result: TestResults = {
          suite: suite.name,
          passed,
          duration,
          coverage,
          errors: passed ? undefined : [stderr]
        }

        if (passed) {
          console.log(`✅ ${suite.name} completed successfully in ${duration}ms`)
          if (coverage !== undefined) {
            console.log(`📊 Coverage: ${coverage}%`)
          }
        } else {
          console.log(`❌ ${suite.name} failed in ${duration}ms`)
        }

        resolve(result)
      })
    })
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting PeopleNest Authentication System Test Suite')
    console.log('=' .repeat(60))

    const testSuites: TestSuite[] = [
      {
        name: 'Unit Tests - Audit Service',
        description: 'Testing audit logging, security monitoring, and GDPR compliance',
        command: 'npm',
        args: ['run', 'test', 'src/tests/services/auditService.test.ts'],
        timeout: 120000
      },
      {
        name: 'Unit Tests - MFA Service',
        description: 'Testing multi-factor authentication functionality',
        command: 'npm',
        args: ['run', 'test', 'src/tests/services/mfaService.test.ts'],
        timeout: 120000
      },
      {
        name: 'Integration Tests - Authentication Routes',
        description: 'Testing complete authentication flows and API endpoints',
        command: 'npm',
        args: ['run', 'test', 'src/tests/routes/auth.integration.test.ts'],
        timeout: 180000
      },
      {
        name: 'Performance Tests - Authentication',
        description: 'Testing authentication system performance and scalability',
        command: 'npm',
        args: ['run', 'test', 'src/tests/performance/auth.performance.test.ts'],
        timeout: 300000
      },
      {
        name: 'Security Tests',
        description: 'Testing security features, rate limiting, and vulnerability protection',
        command: 'npm',
        args: ['run', 'test:security'],
        timeout: 180000
      },
      {
        name: 'Coverage Report',
        description: 'Generating comprehensive test coverage report',
        command: 'npm',
        args: ['run', 'test:coverage'],
        timeout: 240000
      }
    ]

    // Run each test suite
    for (const suite of testSuites) {
      const result = await this.runTestSuite(suite)
      this.results.push(result)
    }

    // Generate final report
    this.generateReport()
  }

  private generateReport(): void {
    const totalDuration = Date.now() - this.startTime
    const passedTests = this.results.filter(r => r.passed).length
    const totalTests = this.results.length
    const overallPassed = passedTests === totalTests

    console.log('\n' + '='.repeat(60))
    console.log('📊 TEST EXECUTION SUMMARY')
    console.log('='.repeat(60))

    console.log(`⏱️  Total Duration: ${Math.round(totalDuration / 1000)}s`)
    console.log(`✅ Passed: ${passedTests}/${totalTests}`)
    console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`)

    if (overallPassed) {
      console.log('\n🎉 ALL TESTS PASSED! Authentication system is ready for deployment.')
    } else {
      console.log('\n⚠️  SOME TESTS FAILED! Please review the errors above.')
    }

    // Detailed results
    console.log('\n📋 DETAILED RESULTS:')
    console.log('-'.repeat(60))
    
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌'
      const duration = Math.round(result.duration / 1000)
      const coverage = result.coverage ? ` (${result.coverage}% coverage)` : ''
      
      console.log(`${status} ${result.suite}: ${duration}s${coverage}`)
      
      if (!result.passed && result.errors) {
        result.errors.forEach(error => {
          console.log(`   Error: ${error.substring(0, 100)}...`)
        })
      }
    })

    // Coverage summary
    const coverageResults = this.results.filter(r => r.coverage !== undefined)
    if (coverageResults.length > 0) {
      const avgCoverage = coverageResults.reduce((sum, r) => sum + (r.coverage || 0), 0) / coverageResults.length
      console.log(`\n📈 Average Coverage: ${avgCoverage.toFixed(1)}%`)
      
      if (avgCoverage >= 90) {
        console.log('🏆 Excellent coverage! Well done!')
      } else if (avgCoverage >= 80) {
        console.log('👍 Good coverage! Consider adding more tests for critical paths.')
      } else {
        console.log('⚠️  Coverage below target. Please add more comprehensive tests.')
      }
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:')
    console.log('-'.repeat(60))
    
    if (overallPassed) {
      console.log('• All authentication tests are passing')
      console.log('• System is ready for integration testing')
      console.log('• Consider running load tests in staging environment')
      console.log('• Review security audit logs for any anomalies')
    } else {
      console.log('• Fix failing tests before proceeding')
      console.log('• Review error logs for specific issues')
      console.log('• Ensure all dependencies are properly mocked')
      console.log('• Check database connectivity and test data setup')
    }

    // Exit with appropriate code
    process.exit(overallPassed ? 0 : 1)
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
PeopleNest Authentication Test Runner

Usage: ts-node src/tests/runTests.ts [options]

Options:
  --help, -h     Show this help message
  --verbose, -v  Enable verbose output
  --coverage     Run with coverage reporting
  --performance  Run only performance tests
  --security     Run only security tests
  --unit         Run only unit tests
  --integration  Run only integration tests

Examples:
  ts-node src/tests/runTests.ts                    # Run all tests
  ts-node src/tests/runTests.ts --unit             # Run only unit tests
  ts-node src/tests/runTests.ts --coverage         # Run with coverage
  ts-node src/tests/runTests.ts --performance      # Run performance tests only
`)
    process.exit(0)
  }

  const runner = new TestRunner()
  
  try {
    await runner.runAllTests()
  } catch (error) {
    console.error('❌ Test runner failed:', error)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

export { TestRunner, TestSuite, TestResults }
