{"total": {"lines": {"total": 3372, "covered": 266, "skipped": 0, "pct": 7.88}, "statements": {"total": 3481, "covered": 270, "skipped": 0, "pct": 7.75}, "functions": {"total": 520, "covered": 36, "skipped": 0, "pct": 6.92}, "branches": {"total": 1206, "covered": 87, "skipped": 0, "pct": 7.21}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "C:\\PeopleNest\\backend\\src\\database\\migrationRunner.ts": {"lines": {"total": 102, "covered": 8, "skipped": 0, "pct": 7.84}, "functions": {"total": 23, "covered": 1, "skipped": 0, "pct": 4.34}, "statements": {"total": 104, "covered": 8, "skipped": 0, "pct": 7.69}, "branches": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\database\\seed.ts": {"lines": {"total": 112, "covered": 7, "skipped": 0, "pct": 6.25}, "functions": {"total": 18, "covered": 1, "skipped": 0, "pct": 5.55}, "statements": {"total": 116, "covered": 7, "skipped": 0, "pct": 6.03}, "branches": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\database\\setup.ts": {"lines": {"total": 120, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 123, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 29, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\middleware\\audit.ts": {"lines": {"total": 98, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 119, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 85, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\middleware\\auth.ts": {"lines": {"total": 82, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 88, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 40, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\middleware\\errorHandler.ts": {"lines": {"total": 135, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 146, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\middleware\\permissions.ts": {"lines": {"total": 98, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 106, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 45, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\middleware\\security.ts": {"lines": {"total": 114, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 125, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\ai.ts": {"lines": {"total": 126, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 126, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\audit.ts": {"lines": {"total": 95, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 100, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\auth.ts": {"lines": {"total": 129, "covered": 52, "skipped": 0, "pct": 40.31}, "functions": {"total": 7, "covered": 4, "skipped": 0, "pct": 57.14}, "statements": {"total": 129, "covered": 52, "skipped": 0, "pct": 40.31}, "branches": {"total": 49, "covered": 11, "skipped": 0, "pct": 22.44}}, "C:\\PeopleNest\\backend\\src\\routes\\departments.ts": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\employees.ts": {"lines": {"total": 102, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 102, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\mfa.ts": {"lines": {"total": 91, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 91, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\onboarding.ts": {"lines": {"total": 69, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 69, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\routes\\positions.ts": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\aiProviderService.ts": {"lines": {"total": 62, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 62, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\aiService.ts": {"lines": {"total": 200, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 50, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 213, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\auditService.ts": {"lines": {"total": 108, "covered": 66, "skipped": 0, "pct": 61.11}, "functions": {"total": 22, "covered": 15, "skipped": 0, "pct": 68.18}, "statements": {"total": 116, "covered": 68, "skipped": 0, "pct": 58.62}, "branches": {"total": 80, "covered": 39, "skipped": 0, "pct": 48.75}}, "C:\\PeopleNest\\backend\\src\\services\\authService.ts": {"lines": {"total": 76, "covered": 6, "skipped": 0, "pct": 7.89}, "functions": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 77, "covered": 6, "skipped": 0, "pct": 7.79}, "branches": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\databaseService.ts": {"lines": {"total": 108, "covered": 9, "skipped": 0, "pct": 8.33}, "functions": {"total": 21, "covered": 1, "skipped": 0, "pct": 4.76}, "statements": {"total": 109, "covered": 9, "skipped": 0, "pct": 8.25}, "branches": {"total": 42, "covered": 10, "skipped": 0, "pct": 23.8}}, "C:\\PeopleNest\\backend\\src\\services\\departmentService.ts": {"lines": {"total": 119, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 120, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\documentService.ts": {"lines": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "C:\\PeopleNest\\backend\\src\\services\\emailService.ts": {"lines": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\employeeService.ts": {"lines": {"total": 153, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 153, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\jwtService.ts": {"lines": {"total": 115, "covered": 28, "skipped": 0, "pct": 24.34}, "functions": {"total": 16, "covered": 4, "skipped": 0, "pct": 25}, "statements": {"total": 115, "covered": 28, "skipped": 0, "pct": 24.34}, "branches": {"total": 39, "covered": 12, "skipped": 0, "pct": 30.76}}, "C:\\PeopleNest\\backend\\src\\services\\mfaService.ts": {"lines": {"total": 99, "covered": 65, "skipped": 0, "pct": 65.65}, "functions": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "statements": {"total": 100, "covered": 66, "skipped": 0, "pct": 66}, "branches": {"total": 30, "covered": 12, "skipped": 0, "pct": 40}}, "C:\\PeopleNest\\backend\\src\\services\\nlQueryService.ts": {"lines": {"total": 129, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 129, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 75, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\notificationService.ts": {"lines": {"total": 31, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\offboardingService.ts": {"lines": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "C:\\PeopleNest\\backend\\src\\services\\onboardingService.ts": {"lines": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 94, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\services\\performanceService.ts": {"lines": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "C:\\PeopleNest\\backend\\src\\services\\positionService.ts": {"lines": {"total": 134, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\utils\\auditLogger.ts": {"lines": {"total": 72, "covered": 6, "skipped": 0, "pct": 8.33}, "functions": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 74, "covered": 7, "skipped": 0, "pct": 9.45}, "branches": {"total": 36, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\utils\\encryption.ts": {"lines": {"total": 85, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 85, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\PeopleNest\\backend\\src\\utils\\logger.ts": {"lines": {"total": 42, "covered": 19, "skipped": 0, "pct": 45.23}, "functions": {"total": 21, "covered": 1, "skipped": 0, "pct": 4.76}, "statements": {"total": 42, "covered": 19, "skipped": 0, "pct": 45.23}, "branches": {"total": 7, "covered": 3, "skipped": 0, "pct": 42.85}}}