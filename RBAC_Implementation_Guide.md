# RBAC Implementation Guide for PeopleNest

## Overview

This guide provides practical implementation steps for enhancing the Role-Based Access Control (RBAC) system in PeopleNest, based on the comprehensive research analysis.

## Current State Analysis

### Existing RBAC Structure

<augment_code_snippet path="backend/src/middleware/auth.ts" mode="EXCERPT">
````typescript
async function getUserPermissions(role: string): Promise<string[]> {
  const rolePermissions: Record<string, string[]> = {
    'super_admin': [
      'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
    ],
    'hr_admin': [
      'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
    ],
    'hr': [
      'hr', 'employee', 'recruiter'
    ],
    'manager': [
      'manager', 'employee'
    ],
    'employee': [
      'employee'
    ]
  }
  return rolePermissions[role] || ['employee']
}
````
</augment_code_snippet>

### Current Permission Middleware

<augment_code_snippet path="backend/src/middleware/permissions.ts" mode="EXCERPT">
````typescript
export const requirePermission = (...requiredPermissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const hasPermission = requiredPermissions.some(permission => 
      req.user.permissions.includes(permission)
    )

    if (!hasPermission) {
      logger.warn('Access denied - insufficient permissions', {
        userId: req.user.id,
        requiredPermissions,
        userPermissions: req.user.permissions
      })
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      })
    }
    next()
  }
}
````
</augment_code_snippet>

## Implementation Phases

### Phase 1: Enhanced Permission System (Week 1-2)

#### 1.1 Granular Permission Structure

Create a new permission service with granular control:

```typescript
// services/PermissionService.ts
interface Permission {
  resource: string;    // 'employee', 'department', 'payroll'
  action: string;      // 'create', 'read', 'update', 'delete'
  scope: string;       // 'own', 'team', 'department', 'all'
  conditions?: string[]; // Additional conditions
}

class PermissionService {
  private static permissions: Map<string, Permission> = new Map([
    ['employee:read:own', { resource: 'employee', action: 'read', scope: 'own' }],
    ['employee:read:team', { resource: 'employee', action: 'read', scope: 'team' }],
    ['employee:read:all', { resource: 'employee', action: 'read', scope: 'all' }],
    ['employee:update:own', { resource: 'employee', action: 'update', scope: 'own' }],
    ['employee:update:team', { resource: 'employee', action: 'update', scope: 'team' }],
    ['payroll:read:department', { resource: 'payroll', action: 'read', scope: 'department' }],
    ['payroll:process:all', { resource: 'payroll', action: 'process', scope: 'all', conditions: ['mfa_required'] }],
  ]);

  static getPermission(permissionKey: string): Permission | undefined {
    return this.permissions.get(permissionKey);
  }

  static hasPermission(userPermissions: string[], requiredPermission: string): boolean {
    return userPermissions.includes(requiredPermission);
  }

  static checkResourceAccess(
    user: User, 
    resource: string, 
    action: string, 
    resourceId?: string
  ): boolean {
    const permissionKey = `${resource}:${action}`;
    
    // Check for 'all' scope permission
    if (user.permissions.includes(`${permissionKey}:all`)) {
      return true;
    }

    // Check for 'own' scope permission
    if (resourceId && user.permissions.includes(`${permissionKey}:own`)) {
      return this.isResourceOwner(user, resource, resourceId);
    }

    // Check for 'team' scope permission
    if (resourceId && user.permissions.includes(`${permissionKey}:team`)) {
      return this.isResourceInTeam(user, resource, resourceId);
    }

    // Check for 'department' scope permission
    if (resourceId && user.permissions.includes(`${permissionKey}:department`)) {
      return this.isResourceInDepartment(user, resource, resourceId);
    }

    return false;
  }

  private static isResourceOwner(user: User, resource: string, resourceId: string): boolean {
    // Implementation depends on resource type
    if (resource === 'employee') {
      return user.employeeId === resourceId;
    }
    return false;
  }

  private static isResourceInTeam(user: User, resource: string, resourceId: string): boolean {
    // Implementation for team-based access
    return false; // Placeholder
  }

  private static isResourceInDepartment(user: User, resource: string, resourceId: string): boolean {
    // Implementation for department-based access
    return false; // Placeholder
  }
}
```

#### 1.2 Enhanced RBAC Middleware

```typescript
// middleware/enhancedRBAC.ts
export const requireResourcePermission = (
  resource: string, 
  action: string, 
  options: {
    resourceIdParam?: string;
    allowSelfAccess?: boolean;
    requireMFA?: boolean;
  } = {}
) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined;
    
    // Check if user has permission for this resource and action
    const hasAccess = PermissionService.checkResourceAccess(
      req.user, 
      resource, 
      action, 
      resourceId
    );

    if (!hasAccess) {
      // Check for self-access if allowed
      if (options.allowSelfAccess && resourceId === req.user.employeeId) {
        return next();
      }

      logger.warn('Access denied - insufficient resource permissions', {
        userId: req.user.id,
        resource,
        action,
        resourceId,
        userPermissions: req.user.permissions
      });

      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions for this resource'
      });
    }

    // Check MFA requirement
    if (options.requireMFA) {
      const mfaCompleted = await checkMFAStatus(req.user.sessionId);
      if (!mfaCompleted) {
        return res.status(403).json({
          error: 'MFA Required',
          message: 'Multi-factor authentication required for this operation'
        });
      }
    }

    next();
  };
};

async function checkMFAStatus(sessionId: string): Promise<boolean> {
  // Implementation to check MFA status
  return true; // Placeholder
}
```

#### 1.3 Database Schema Updates

```sql
-- Create permissions table
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    scope VARCHAR(50) NOT NULL,
    description TEXT,
    requires_mfa BOOLEAN DEFAULT false,
    is_sensitive BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create role_permissions junction table
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id VARCHAR(50) NOT NULL,
    permission_id UUID NOT NULL REFERENCES permissions(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- Insert base permissions
INSERT INTO permissions (name, resource, action, scope, description, requires_mfa) VALUES
('employee:read:own', 'employee', 'read', 'own', 'Read own employee data', false),
('employee:read:team', 'employee', 'read', 'team', 'Read team member data', false),
('employee:read:all', 'employee', 'read', 'all', 'Read all employee data', false),
('employee:update:own', 'employee', 'update', 'own', 'Update own employee data', false),
('employee:update:team', 'employee', 'update', 'team', 'Update team member data', false),
('employee:update:all', 'employee', 'update', 'all', 'Update any employee data', false),
('payroll:read:own', 'payroll', 'read', 'own', 'Read own payroll data', false),
('payroll:read:department', 'payroll', 'read', 'department', 'Read department payroll data', true),
('payroll:read:all', 'payroll', 'read', 'all', 'Read all payroll data', true),
('payroll:process:all', 'payroll', 'process', 'all', 'Process payroll', true),
('department:read:all', 'department', 'read', 'all', 'Read department data', false),
('department:write:all', 'department', 'write', 'all', 'Manage departments', false),
('system:admin:all', 'system', 'admin', 'all', 'System administration', true);

-- Map permissions to roles
INSERT INTO role_permissions (role_id, permission_id) 
SELECT 'employee', id FROM permissions WHERE name IN ('employee:read:own', 'employee:update:own', 'payroll:read:own');

INSERT INTO role_permissions (role_id, permission_id) 
SELECT 'manager', id FROM permissions WHERE name IN ('employee:read:own', 'employee:update:own', 'employee:read:team', 'employee:update:team', 'payroll:read:own');

INSERT INTO role_permissions (role_id, permission_id) 
SELECT 'hr', id FROM permissions WHERE name IN ('employee:read:all', 'employee:update:all', 'department:read:all', 'payroll:read:department');

INSERT INTO role_permissions (role_id, permission_id) 
SELECT 'hr_admin', id FROM permissions WHERE name IN ('employee:read:all', 'employee:update:all', 'department:read:all', 'department:write:all', 'payroll:read:all');

INSERT INTO role_permissions (role_id, permission_id) 
SELECT 'super_admin', id FROM permissions;
```

### Phase 2: Context-Aware Access Control (Week 3-4)

#### 2.1 Conditional Access Policies

```typescript
// services/AccessPolicyService.ts
interface AccessCondition {
  type: 'time' | 'location' | 'device' | 'mfa' | 'approval';
  parameters: Record<string, any>;
}

interface AccessPolicy {
  id: string;
  permission: string;
  conditions: AccessCondition[];
  isActive: boolean;
}

class AccessPolicyService {
  private static policies: AccessPolicy[] = [
    {
      id: 'payroll-time-restriction',
      permission: 'payroll:process:all',
      conditions: [
        {
          type: 'time',
          parameters: {
            startTime: '09:00',
            endTime: '17:00',
            timezone: 'UTC',
            daysOfWeek: [1, 2, 3, 4, 5] // Monday to Friday
          }
        },
        {
          type: 'mfa',
          parameters: { required: true }
        }
      ],
      isActive: true
    },
    {
      id: 'sensitive-data-access',
      permission: 'employee:read:all',
      conditions: [
        {
          type: 'device',
          parameters: { trustedOnly: true }
        },
        {
          type: 'location',
          parameters: { 
            allowedCountries: ['US', 'CA'],
            blockedIPs: []
          }
        }
      ],
      isActive: true
    }
  ];

  static async evaluateAccess(
    user: User,
    permission: string,
    context: AccessContext
  ): Promise<{ allowed: boolean; reason?: string }> {
    const policies = this.policies.filter(p => 
      p.permission === permission && p.isActive
    );

    if (policies.length === 0) {
      return { allowed: true };
    }

    for (const policy of policies) {
      const result = await this.evaluatePolicy(policy, context);
      if (!result.allowed) {
        return result;
      }
    }

    return { allowed: true };
  }

  private static async evaluatePolicy(
    policy: AccessPolicy,
    context: AccessContext
  ): Promise<{ allowed: boolean; reason?: string }> {
    for (const condition of policy.conditions) {
      const result = await this.evaluateCondition(condition, context);
      if (!result.allowed) {
        return result;
      }
    }
    return { allowed: true };
  }

  private static async evaluateCondition(
    condition: AccessCondition,
    context: AccessContext
  ): Promise<{ allowed: boolean; reason?: string }> {
    switch (condition.type) {
      case 'time':
        return this.evaluateTimeCondition(condition.parameters, context);
      case 'location':
        return this.evaluateLocationCondition(condition.parameters, context);
      case 'device':
        return this.evaluateDeviceCondition(condition.parameters, context);
      case 'mfa':
        return this.evaluateMFACondition(condition.parameters, context);
      default:
        return { allowed: true };
    }
  }

  private static evaluateTimeCondition(
    params: any,
    context: AccessContext
  ): { allowed: boolean; reason?: string } {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5);
    const currentDay = now.getDay();

    if (params.daysOfWeek && !params.daysOfWeek.includes(currentDay)) {
      return { allowed: false, reason: 'Access not allowed on this day' };
    }

    if (params.startTime && params.endTime) {
      if (currentTime < params.startTime || currentTime > params.endTime) {
        return { allowed: false, reason: 'Access not allowed at this time' };
      }
    }

    return { allowed: true };
  }

  private static evaluateLocationCondition(
    params: any,
    context: AccessContext
  ): { allowed: boolean; reason?: string } {
    // Implementation for location-based access control
    return { allowed: true };
  }

  private static evaluateDeviceCondition(
    params: any,
    context: AccessContext
  ): { allowed: boolean; reason?: string } {
    if (params.trustedOnly && !context.isTrustedDevice) {
      return { allowed: false, reason: 'Access requires a trusted device' };
    }
    return { allowed: true };
  }

  private static evaluateMFACondition(
    params: any,
    context: AccessContext
  ): { allowed: boolean; reason?: string } {
    if (params.required && !context.mfaCompleted) {
      return { allowed: false, reason: 'Multi-factor authentication required' };
    }
    return { allowed: true };
  }
}

interface AccessContext {
  userId: string;
  sessionId: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  isTrustedDevice: boolean;
  mfaCompleted: boolean;
  geolocation?: {
    country: string;
    region: string;
    city: string;
  };
}
```

### Phase 3: Advanced Security Features (Week 5-6)

#### 3.1 Multi-Factor Authentication Integration

```typescript
// routes/mfa.ts
import express from 'express';
import { authMiddleware } from '../middleware/auth';
import { MFAService } from '../services/MFAService';

const router = express.Router();
const mfaService = new MFAService();

// Setup MFA
router.post('/setup', authMiddleware, async (req, res) => {
  try {
    const { user } = req;
    const mfaSecret = await mfaService.generateSecret(user.id, user.email);
    
    res.json({
      message: 'MFA setup initiated',
      qrCode: mfaSecret.qrCode,
      backupCodes: mfaSecret.backupCodes,
      secret: mfaSecret.secret // Only for initial setup
    });
  } catch (error) {
    res.status(500).json({
      error: 'MFA Setup Failed',
      message: error.message
    });
  }
});

// Verify MFA token
router.post('/verify', authMiddleware, async (req, res) => {
  try {
    const { token } = req.body;
    const { user } = req;
    
    const isValid = await mfaService.verifyToken({
      token,
      userId: user.id,
      sessionId: user.sessionId
    });

    if (isValid) {
      res.json({
        message: 'MFA verification successful',
        mfaCompleted: true
      });
    } else {
      res.status(401).json({
        error: 'Invalid MFA Token',
        message: 'The provided token is invalid or expired'
      });
    }
  } catch (error) {
    res.status(500).json({
      error: 'MFA Verification Failed',
      message: error.message
    });
  }
});

export default router;
```

#### 3.2 Audit Logging Enhancement

```typescript
// services/AuditService.ts
interface AuditEvent {
  eventType: string;
  eventCategory: 'authentication' | 'authorization' | 'data_access' | 'system';
  severity: 'info' | 'warning' | 'error' | 'critical';
  userId?: string;
  sessionId?: string;
  resource?: string;
  action?: string;
  resourceId?: string;
  result: 'success' | 'failure' | 'denied';
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, any>;
}

class AuditService {
  static async logEvent(event: AuditEvent): Promise<void> {
    try {
      await db.query(`
        INSERT INTO security_audit_log (
          event_type, event_category, severity, user_id, session_id,
          resource, action, resource_id, result, ip_address, user_agent,
          metadata, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
      `, [
        event.eventType,
        event.eventCategory,
        event.severity,
        event.userId,
        event.sessionId,
        event.resource,
        event.action,
        event.resourceId,
        event.result,
        event.ipAddress,
        event.userAgent,
        JSON.stringify(event.metadata || {})
      ]);

      // Real-time alerting for critical events
      if (event.severity === 'critical') {
        await this.sendSecurityAlert(event);
      }
    } catch (error) {
      console.error('Failed to log audit event:', error);
    }
  }

  private static async sendSecurityAlert(event: AuditEvent): Promise<void> {
    // Implementation for real-time security alerts
    // Could integrate with Slack, email, or security monitoring systems
  }

  static async getAuditTrail(
    filters: {
      userId?: string;
      resource?: string;
      startDate?: Date;
      endDate?: Date;
      eventType?: string;
    },
    pagination: { page: number; limit: number }
  ): Promise<any[]> {
    let query = 'SELECT * FROM security_audit_log WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;

    if (filters.userId) {
      query += ` AND user_id = $${paramIndex++}`;
      params.push(filters.userId);
    }

    if (filters.resource) {
      query += ` AND resource = $${paramIndex++}`;
      params.push(filters.resource);
    }

    if (filters.startDate) {
      query += ` AND timestamp >= $${paramIndex++}`;
      params.push(filters.startDate);
    }

    if (filters.endDate) {
      query += ` AND timestamp <= $${paramIndex++}`;
      params.push(filters.endDate);
    }

    query += ` ORDER BY timestamp DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
    params.push(pagination.limit, (pagination.page - 1) * pagination.limit);

    const result = await db.query(query, params);
    return result.rows;
  }
}

// Enhanced audit middleware
export const auditMiddleware = (
  eventType: string,
  eventCategory: AuditEvent['eventCategory'] = 'data_access'
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      const result = res.statusCode < 400 ? 'success' : 'failure';
      const severity = res.statusCode >= 500 ? 'error' : 
                      res.statusCode >= 400 ? 'warning' : 'info';

      AuditService.logEvent({
        eventType,
        eventCategory,
        severity,
        userId: req.user?.id,
        sessionId: req.user?.sessionId,
        resource: req.route?.path,
        action: req.method,
        resourceId: req.params.id,
        result,
        ipAddress: req.ip || req.connection.remoteAddress || '',
        userAgent: req.get('User-Agent') || '',
        metadata: {
          statusCode: res.statusCode,
          responseSize: data ? data.length : 0,
          duration: Date.now() - req.startTime
        }
      });

      return originalSend.call(this, data);
    };

    req.startTime = Date.now();
    next();
  };
};
```

## Usage Examples

### Route Protection with Enhanced RBAC

```typescript
// routes/employees.ts
import { requireResourcePermission } from '../middleware/enhancedRBAC';
import { auditMiddleware } from '../middleware/audit';

// Get employee data - context-aware access
router.get('/:id', 
  authMiddleware,
  requireResourcePermission('employee', 'read', { 
    resourceIdParam: 'id',
    allowSelfAccess: true 
  }),
  auditMiddleware('employee_access', 'data_access'),
  async (req, res) => {
    // Route implementation
  }
);

// Update employee salary - requires MFA
router.put('/:id/salary',
  authMiddleware,
  requireResourcePermission('employee', 'update_salary', {
    resourceIdParam: 'id',
    requireMFA: true
  }),
  auditMiddleware('salary_update', 'data_access'),
  async (req, res) => {
    // Route implementation
  }
);

// Process payroll - time and location restricted
router.post('/payroll/process',
  authMiddleware,
  requireResourcePermission('payroll', 'process', {
    requireMFA: true
  }),
  auditMiddleware('payroll_process', 'system'),
  async (req, res) => {
    // Route implementation
  }
);
```

This implementation guide provides a practical roadmap for enhancing the RBAC system in PeopleNest with modern security features and enterprise-grade access controls.
