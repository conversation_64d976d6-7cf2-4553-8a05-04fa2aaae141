import { Request, Response, NextFunction } from 'express'
import { auditService, securityMonitoringService, AuditEvent } from '../services/auditService'
import { logger } from '../utils/logger'

/**
 * Enhanced audit middleware that integrates with the new audit service
 */
export const auditMiddleware = (
  eventType: string,
  eventCategory: AuditEvent['eventCategory'] = 'data_access',
  options: {
    dataClassification?: 'public' | 'internal' | 'confidential' | 'restricted'
    purpose?: string
    legalBasis?: string
    sensitiveOperation?: boolean
    trackDataAccess?: boolean
  } = {}
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now()

    // Store original send method
    const originalSend = res.send

    // Override send method to log after response
    res.send = function(body?: any) {
      const duration = Date.now() - startTime
      const statusCode = res.statusCode
      const success = statusCode >= 200 && statusCode < 400

      // Create audit event
      const auditEvent: AuditEvent = {
        userId: req.user?.id,
        sessionId: req.user?.sessionId,
        eventType,
        eventCategory,
        resource: extractResourceFromPath(req.path),
        resourceId: req.params.id || req.body?.id,
        action: mapMethodToAction(req.method),
        details: {
          path: req.path,
          method: req.method,
          statusCode,
          duration,
          requestBody: sanitizeRequestBody(req.body),
          responseSize: body ? JSON.stringify(body).length : 0,
          purpose: options.purpose,
          legalBasis: options.legalBasis
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date(),
        severity: determineSeverity(eventCategory, statusCode, options.sensitiveOperation),
        success,
        errorMessage: !success ? extractErrorMessage(body) : undefined,
        riskScore: calculateRiskScore(req, eventCategory, options.sensitiveOperation),
        complianceFlags: determineComplianceFlags(options.dataClassification, eventCategory)
      }

      // Log audit event
      auditService.logAuditEvent(auditEvent).catch(error => {
        logger.error('Failed to log audit event:', error)
      })

      // Log data access for GDPR compliance if required
      if (options.trackDataAccess && req.user) {
        auditService.logDataAccess({
          userId: req.user.id,
          sessionId: req.user.sessionId,
          resource: auditEvent.resource || 'unknown',
          resourceId: auditEvent.resourceId,
          action: auditEvent.action as any,
          dataClassification: options.dataClassification || 'internal',
          recordCount: extractRecordCount(req, body),
          fieldAccessed: extractAccessedFields(req, body),
          purpose: options.purpose,
          legalBasis: options.legalBasis,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date()
        }).catch(error => {
          logger.error('Failed to log data access:', error)
        })
      }

      // Analyze for suspicious patterns
      securityMonitoringService.analyzeEvent(auditEvent).catch(error => {
        logger.error('Failed to analyze event for suspicious patterns:', error)
      })

      // Call original send method
      return originalSend.call(this, body)
    }

    next()
  }
}

/**
 * Simplified audit middleware for basic request logging
 */
export const basicAuditMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Only log authenticated requests that modify data
  if (!req.user || !['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
    return next()
  }

  const eventType = `${req.method.toLowerCase()}_${extractResourceFromPath(req.path)}`
  const eventCategory = mapMethodToCategory(req.method)

  return auditMiddleware(eventType, eventCategory, {
    dataClassification: 'internal',
    purpose: 'API Operation',
    legalBasis: 'Legitimate Interest - System Operation'
  })(req, res, next)
}

/**
 * Security event logging middleware
 */
export const securityAuditMiddleware = (
  eventType: string,
  severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const auditEvent: AuditEvent = {
      userId: req.user?.id,
      sessionId: req.user?.sessionId,
      eventType,
      eventCategory: 'security_action',
      details: {
        path: req.path,
        method: req.method,
        requestBody: sanitizeRequestBody(req.body)
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date(),
      severity,
      success: true,
      riskScore: severity === 'critical' ? 90 : severity === 'high' ? 70 : severity === 'medium' ? 50 : 30,
      complianceFlags: ['security']
    }

    auditService.logAuditEvent(auditEvent).catch(error => {
      logger.error('Failed to log security audit event:', error)
    })

    next()
  }
}

// Helper functions

function extractResourceFromPath(path: string): string {
  const segments = path.split('/').filter(Boolean)
  if (segments.length >= 2 && segments[0] === 'api') {
    return segments[1]
  }
  return segments[0] || 'unknown'
}

function mapMethodToAction(method: string): string {
  switch (method.toUpperCase()) {
    case 'GET': return 'read'
    case 'POST': return 'create'
    case 'PUT': return 'update'
    case 'PATCH': return 'update'
    case 'DELETE': return 'delete'
    default: return method.toLowerCase()
  }
}

function mapMethodToCategory(method: string): AuditEvent['eventCategory'] {
  switch (method.toUpperCase()) {
    case 'GET': return 'data_access'
    case 'POST': return 'data_modification'
    case 'PUT': return 'data_modification'
    case 'PATCH': return 'data_modification'
    case 'DELETE': return 'data_modification'
    default: return 'system_event'
  }
}

function sanitizeRequestBody(body: any): any {
  if (!body) return undefined

  const sanitized = { ...body }
  
  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'ssn', 'national_id']
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]'
    }
  })

  return sanitized
}

function determineSeverity(
  category: AuditEvent['eventCategory'],
  statusCode: number,
  sensitiveOperation?: boolean
): AuditEvent['severity'] {
  if (statusCode >= 500) return 'high'
  if (statusCode >= 400) return 'medium'
  if (sensitiveOperation) return 'high'
  if (category === 'security_action') return 'medium'
  if (category === 'administrative_action') return 'medium'
  return 'low'
}

function calculateRiskScore(
  req: Request,
  category: AuditEvent['eventCategory'],
  sensitiveOperation?: boolean
): number {
  let score = 0

  // Base score by category
  switch (category) {
    case 'authentication': score += 30; break
    case 'authorization': score += 40; break
    case 'data_access': score += 20; break
    case 'data_modification': score += 50; break
    case 'administrative_action': score += 60; break
    case 'security_action': score += 70; break
    default: score += 10
  }

  // Increase for sensitive operations
  if (sensitiveOperation) score += 30

  // Increase for off-hours access
  const hour = new Date().getHours()
  if (hour < 6 || hour > 18) score += 20

  // Increase for admin operations
  if (req.user?.role === 'super_admin') score += 10

  return Math.min(score, 100)
}

function determineComplianceFlags(
  dataClassification?: string,
  category?: AuditEvent['eventCategory']
): string[] {
  const flags: string[] = []

  if (dataClassification === 'confidential' || dataClassification === 'restricted') {
    flags.push('gdpr', 'data_protection')
  }

  if (category === 'data_access' || category === 'data_modification') {
    flags.push('data_protection')
  }

  if (category === 'security_action') {
    flags.push('security')
  }

  return flags
}

function extractErrorMessage(body: any): string | undefined {
  if (typeof body === 'object' && body?.error) {
    return typeof body.error === 'string' ? body.error : body.error.message
  }
  return undefined
}

function extractRecordCount(req: Request, responseBody: any): number {
  // Try to extract record count from response
  if (responseBody?.data?.length) return responseBody.data.length
  if (responseBody?.data && typeof responseBody.data === 'object') return 1
  if (req.method === 'GET' && responseBody) return 1
  return 1
}

function extractAccessedFields(req: Request, responseBody: any): string[] {
  // This would be more sophisticated in a real implementation
  // For now, return common fields based on the resource
  const resource = extractResourceFromPath(req.path)
  
  switch (resource) {
    case 'employees':
      return ['id', 'email', 'first_name', 'last_name', 'department']
    case 'users':
      return ['id', 'email', 'role', 'last_login']
    default:
      return ['id']
  }
}

export default {
  auditMiddleware,
  basicAuditMiddleware,
  securityAuditMiddleware
}
