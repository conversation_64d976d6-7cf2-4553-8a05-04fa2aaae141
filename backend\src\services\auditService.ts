import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'

export interface AuditEvent {
  userId?: string
  sessionId?: string
  eventType: string
  eventCategory: 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'administrative_action' | 'security_action' | 'system_event'
  resource?: string
  resourceId?: string
  action?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: Date
  severity: 'low' | 'medium' | 'high' | 'critical'
  success: boolean
  errorMessage?: string
  riskScore?: number
  complianceFlags?: string[]
}

export interface SecurityEvent {
  userId?: string
  sessionId?: string
  eventType: string
  details: Record<string, any>
  ipAddress: string
  userAgent: string
  timestamp: Date
  severity?: 'low' | 'medium' | 'high' | 'critical'
  riskScore?: number
}

export interface DataAccessEvent {
  userId: string
  sessionId?: string
  resource: string
  resourceId?: string
  action: 'read' | 'create' | 'update' | 'delete' | 'export' | 'print'
  dataClassification: 'public' | 'internal' | 'confidential' | 'restricted'
  recordCount?: number
  fieldAccessed?: string[]
  purpose?: string
  legalBasis?: string
  ipAddress?: string
  userAgent?: string
  timestamp: Date
}

export interface ComplianceReport {
  reportType: 'gdpr_access' | 'data_retention' | 'security_incidents' | 'user_activity'
  startDate: Date
  endDate: Date
  userId?: string
  filters?: Record<string, any>
}

export class AuditService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  /**
   * Log general audit event
   */
  async logAuditEvent(event: AuditEvent): Promise<void> {
    try {
      await this.db.query(`
        INSERT INTO audit_events (
          user_id, session_id, event_type, event_category, resource, resource_id,
          action, details, ip_address, user_agent, timestamp, severity, success,
          error_message, risk_score, compliance_flags
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      `, [
        event.userId,
        event.sessionId,
        event.eventType,
        event.eventCategory,
        event.resource,
        event.resourceId,
        event.action,
        JSON.stringify(event.details || {}),
        event.ipAddress,
        event.userAgent,
        event.timestamp,
        event.severity,
        event.success,
        event.errorMessage,
        event.riskScore || 0,
        event.complianceFlags || []
      ])

      // Log high-severity events to application logger
      if (event.severity === 'high' || event.severity === 'critical') {
        logger.warn('High-severity audit event', {
          eventType: event.eventType,
          userId: event.userId,
          severity: event.severity,
          details: event.details
        })
      }

    } catch (error) {
      logger.error('Failed to log audit event:', error)
      // Don't throw - audit logging should not break application flow
    }
  }

  /**
   * Log security-specific event
   */
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      const auditEvent: AuditEvent = {
        userId: event.userId,
        sessionId: event.sessionId,
        eventType: event.eventType,
        eventCategory: 'security_action',
        details: event.details,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        timestamp: event.timestamp,
        severity: event.severity || 'medium',
        success: true,
        riskScore: event.riskScore
      }

      await this.logAuditEvent(auditEvent)

      // Also log to security events table for specialized monitoring
      await this.db.query(`
        INSERT INTO security_events (
          user_id, session_id, event_type, details, ip_address, user_agent,
          timestamp, severity, risk_score
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        event.userId,
        event.sessionId,
        event.eventType,
        JSON.stringify(event.details),
        event.ipAddress,
        event.userAgent,
        event.timestamp,
        event.severity || 'medium',
        event.riskScore || 0
      ])

    } catch (error) {
      logger.error('Failed to log security event:', error)
    }
  }

  /**
   * Log data access for GDPR compliance
   */
  async logDataAccess(event: DataAccessEvent): Promise<void> {
    try {
      await this.db.query(`
        INSERT INTO data_access_log (
          user_id, session_id, resource, resource_id, action, data_classification,
          record_count, fields_accessed, purpose, legal_basis, ip_address,
          user_agent, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      `, [
        event.userId,
        event.sessionId,
        event.resource,
        event.resourceId,
        event.action,
        event.dataClassification,
        event.recordCount || 1,
        event.fieldAccessed || [],
        event.purpose,
        event.legalBasis,
        event.ipAddress,
        event.userAgent,
        event.timestamp
      ])

      // Also log as general audit event
      await this.logAuditEvent({
        userId: event.userId,
        sessionId: event.sessionId,
        eventType: 'data_access',
        eventCategory: 'data_access',
        resource: event.resource,
        resourceId: event.resourceId,
        action: event.action,
        details: {
          dataClassification: event.dataClassification,
          recordCount: event.recordCount,
          fieldsAccessed: event.fieldAccessed,
          purpose: event.purpose,
          legalBasis: event.legalBasis
        },
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        timestamp: event.timestamp,
        severity: event.dataClassification === 'restricted' ? 'high' : 'medium',
        success: true,
        complianceFlags: ['gdpr', 'data_protection']
      })

    } catch (error) {
      logger.error('Failed to log data access:', error)
    }
  }

  /**
   * Get audit events for user (GDPR compliance)
   */
  async getUserAuditTrail(userId: string, startDate?: Date, endDate?: Date): Promise<any[]> {
    try {
      const query = `
        SELECT event_type, event_category, resource, action, details,
               ip_address, timestamp, severity, success
        FROM audit_events
        WHERE user_id = $1
        ${startDate ? 'AND timestamp >= $2' : ''}
        ${endDate ? `AND timestamp <= $${startDate ? '3' : '2'}` : ''}
        ORDER BY timestamp DESC
        LIMIT 1000
      `

      const params = [userId]
      if (startDate) params.push(startDate.toISOString())
      if (endDate) params.push(endDate.toISOString())

      const result = await this.db.query(query, params)
      return result.rows

    } catch (error) {
      logger.error('Failed to get user audit trail:', error)
      throw error
    }
  }

  /**
   * Get security incidents
   */
  async getSecurityIncidents(severity?: string, limit: number = 100): Promise<any[]> {
    try {
      const query = `
        SELECT user_id, event_type, details, ip_address, timestamp, severity, risk_score
        FROM security_events
        ${severity ? 'WHERE severity = $1' : ''}
        ORDER BY timestamp DESC, risk_score DESC
        LIMIT $${severity ? '2' : '1'}
      `

      const params = severity ? [severity, limit] : [limit]
      const result = await this.db.query(query, params)
      return result.rows

    } catch (error) {
      logger.error('Failed to get security incidents:', error)
      throw error
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(report: ComplianceReport): Promise<any> {
    try {
      switch (report.reportType) {
        case 'gdpr_access':
          return await this.generateGDPRAccessReport(report)
        case 'data_retention':
          return await this.generateDataRetentionReport(report)
        case 'security_incidents':
          return await this.generateSecurityIncidentsReport(report)
        case 'user_activity':
          return await this.generateUserActivityReport(report)
        default:
          throw new Error(`Unknown report type: ${report.reportType}`)
      }
    } catch (error) {
      logger.error('Failed to generate compliance report:', error)
      throw error
    }
  }

  /**
   * Clean up old audit data based on retention policies
   */
  async cleanupAuditData(retentionDays: number = 2555): Promise<void> { // 7 years default
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

      // Archive old data before deletion (if archiving is configured)
      // This would typically export to long-term storage

      // Delete old audit events
      const auditResult = await this.db.query(`
        DELETE FROM audit_events WHERE timestamp < $1
      `, [cutoffDate])

      // Delete old security events
      const securityResult = await this.db.query(`
        DELETE FROM security_events WHERE timestamp < $1
      `, [cutoffDate])

      // Delete old data access logs
      const dataAccessResult = await this.db.query(`
        DELETE FROM data_access_log WHERE timestamp < $1
      `, [cutoffDate])

      logger.info('Audit data cleanup completed', {
        cutoffDate,
        auditEventsDeleted: auditResult.rowCount,
        securityEventsDeleted: securityResult.rowCount,
        dataAccessLogsDeleted: dataAccessResult.rowCount
      })

    } catch (error) {
      logger.error('Failed to cleanup audit data:', error)
      throw error
    }
  }

  /**
   * Private helper methods for compliance reports
   */
  private async generateGDPRAccessReport(report: ComplianceReport): Promise<any> {
    const query = `
      SELECT resource, action, data_classification, record_count,
             fields_accessed, purpose, legal_basis, timestamp
      FROM data_access_log
      WHERE timestamp BETWEEN $1 AND $2
      ${report.userId ? 'AND user_id = $3' : ''}
      ORDER BY timestamp DESC
    `

    const params: any[] = [report.startDate, report.endDate]
    if (report.userId) params.push(report.userId)

    const result = await this.db.query(query, params)
    return {
      reportType: 'gdpr_access',
      period: { start: report.startDate, end: report.endDate },
      totalRecords: result.rowCount,
      data: result.rows
    }
  }

  private async generateDataRetentionReport(report: ComplianceReport): Promise<any> {
    // Implementation for data retention compliance report
    return { reportType: 'data_retention', message: 'Not yet implemented' }
  }

  private async generateSecurityIncidentsReport(report: ComplianceReport): Promise<any> {
    const query = `
      SELECT event_type, severity, risk_score, details, timestamp
      FROM security_events
      WHERE timestamp BETWEEN $1 AND $2
      ORDER BY risk_score DESC, timestamp DESC
    `

    const result = await this.db.query(query, [report.startDate, report.endDate])
    return {
      reportType: 'security_incidents',
      period: { start: report.startDate, end: report.endDate },
      totalIncidents: result.rowCount,
      data: result.rows
    }
  }

  private async generateUserActivityReport(report: ComplianceReport): Promise<any> {
    const query = `
      SELECT user_id, event_type, event_category, resource, action, timestamp
      FROM audit_events
      WHERE timestamp BETWEEN $1 AND $2
      ${report.userId ? 'AND user_id = $3' : ''}
      ORDER BY timestamp DESC
    `

    const params: any[] = [report.startDate, report.endDate]
    if (report.userId) params.push(report.userId)

    const result = await this.db.query(query, params)
    return {
      reportType: 'user_activity',
      period: { start: report.startDate, end: report.endDate },
      totalEvents: result.rowCount,
      data: result.rows
    }
  }
}

/**
 * Security Monitoring Service for detecting suspicious activities
 */
export class SecurityMonitoringService {
  private db: DatabaseService
  private auditService: AuditService

  constructor() {
    this.db = new DatabaseService()
    this.auditService = new AuditService()
  }

  /**
   * Analyze event for suspicious patterns
   */
  async analyzeEvent(event: AuditEvent): Promise<void> {
    try {
      // Get active patterns
      const patterns = await this.getActivePatterns()

      for (const pattern of patterns) {
        const isMatch = await this.checkPatternMatch(event, pattern)
        if (isMatch) {
          await this.triggerAlert(event, pattern)
        }
      }
    } catch (error) {
      logger.error('Failed to analyze event for suspicious patterns:', error)
    }
  }

  /**
   * Check if event matches suspicious pattern
   */
  private async checkPatternMatch(event: AuditEvent, pattern: any): Promise<boolean> {
    const rules = pattern.pattern_rules

    switch (pattern.pattern_type) {
      case 'frequency':
        return await this.checkFrequencyPattern(event, pattern, rules)
      case 'location':
        return await this.checkLocationPattern(event, pattern, rules)
      case 'time':
        return await this.checkTimePattern(event, pattern, rules)
      case 'behavior':
        return await this.checkBehaviorPattern(event, pattern, rules)
      case 'data_volume':
        return await this.checkDataVolumePattern(event, pattern, rules)
      default:
        return false
    }
  }

  private async checkFrequencyPattern(event: AuditEvent, pattern: any, rules: any): Promise<boolean> {
    if (event.eventType !== rules.event_type) return false

    const timeWindow = pattern.time_window_minutes || 15
    const threshold = rules.count_threshold || 5

    const result = await this.db.query(`
      SELECT COUNT(*) as count
      FROM audit_events
      WHERE event_type = $1
      AND ip_address = $2
      AND timestamp >= NOW() - INTERVAL '${timeWindow} minutes'
    `, [event.eventType, event.ipAddress])

    return parseInt(result.rows[0].count) >= threshold
  }

  private async checkLocationPattern(event: AuditEvent, pattern: any, rules: any): Promise<boolean> {
    // Simplified location check - in production would use geolocation services
    if (!event.userId || !event.ipAddress) return false

    const result = await this.db.query(`
      SELECT DISTINCT ip_address
      FROM audit_events
      WHERE user_id = $1
      AND timestamp >= NOW() - INTERVAL '30 days'
      AND ip_address != $2
    `, [event.userId, event.ipAddress])

    // If user has never logged in from this IP, it's suspicious
    return result.rows.length === 0 && event.eventType === 'login_success'
  }

  private async checkTimePattern(event: AuditEvent, pattern: any, rules: any): Promise<boolean> {
    if (rules.time_range !== 'off_hours') return false

    const hour = event.timestamp.getHours()
    // Consider 6 PM to 6 AM as off-hours
    return hour >= 18 || hour <= 6
  }

  private async checkBehaviorPattern(event: AuditEvent, pattern: any, rules: any): Promise<boolean> {
    return event.eventType === rules.event_type &&
           event.eventCategory === 'authorization' &&
           !event.success
  }

  private async checkDataVolumePattern(event: AuditEvent, pattern: any, rules: any): Promise<boolean> {
    if (event.eventType !== rules.event_type) return false

    const recordCount = event.details?.recordCount || 1
    return recordCount >= (rules.record_threshold || 1000)
  }

  private async getActivePatterns(): Promise<any[]> {
    const result = await this.db.query(`
      SELECT * FROM suspicious_activity_patterns WHERE is_active = true
    `)
    return result.rows
  }

  private async triggerAlert(event: AuditEvent, pattern: any): Promise<void> {
    try {
      await this.db.query(`
        INSERT INTO activity_monitoring_alerts (
          pattern_id, user_id, session_id, alert_type, alert_message,
          risk_score, severity, ip_address, user_agent, additional_context
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        pattern.id,
        event.userId,
        event.sessionId,
        pattern.pattern_name,
        `Suspicious activity detected: ${pattern.description}`,
        pattern.risk_score,
        pattern.alert_level,
        event.ipAddress,
        event.userAgent,
        JSON.stringify({ triggering_event: event })
      ])

      // Log security event
      await this.auditService.logSecurityEvent({
        userId: event.userId,
        sessionId: event.sessionId,
        eventType: 'suspicious_activity_detected',
        details: {
          pattern: pattern.pattern_name,
          triggeringEvent: event.eventType,
          riskScore: pattern.risk_score
        },
        ipAddress: event.ipAddress || '',
        userAgent: event.userAgent || '',
        timestamp: new Date(),
        severity: pattern.alert_level,
        riskScore: pattern.risk_score
      })

      logger.warn('Suspicious activity alert triggered', {
        pattern: pattern.pattern_name,
        userId: event.userId,
        eventType: event.eventType,
        riskScore: pattern.risk_score
      })

    } catch (error) {
      logger.error('Failed to trigger security alert:', error)
    }
  }
}

// Export singleton instances
export const auditService = new AuditService()
export const securityMonitoringService = new SecurityMonitoringService()
