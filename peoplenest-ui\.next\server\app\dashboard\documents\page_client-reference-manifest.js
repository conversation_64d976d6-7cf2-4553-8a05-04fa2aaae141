globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/documents/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/recruitment/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/recruitment/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/onboarding/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/onboarding/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/organization/departments/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/organization/departments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/organization/positions/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/organization/positions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/organization/chart/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/organization/chart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/payroll/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/payroll/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/attendance/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/attendance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/leave/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/leave/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/announcements/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/announcements/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/documents/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/employees/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/employees/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/auth-layout-client.tsx":{"*":{"id":"(ssr)/./src/app/auth/auth-layout-client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/performance/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/performance/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/layout.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\recruitment\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/recruitment/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\onboarding\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/onboarding/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\departments\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/organization/departments/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\positions\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/organization/positions/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\chart\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/organization/chart/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\payroll\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/payroll/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\attendance\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/attendance/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\leave\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/leave/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\announcements\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/announcements/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\documents\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/documents/page.tsx","name":"*","chunks":["app/dashboard/documents/page","static/chunks/app/dashboard/documents/page.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\employees\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/employees/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\auth-layout-client.tsx":{"id":"(app-pages-browser)/./src/app/auth/auth-layout-client.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\performance\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/performance/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\PeopleNest\\peoplenest-ui\\src\\":[],"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page":[],"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout":[],"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\page":[],"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\documents\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/recruitment/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/recruitment/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/onboarding/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/onboarding/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/organization/departments/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/organization/departments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/organization/positions/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/organization/positions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/organization/chart/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/organization/chart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/payroll/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/payroll/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/attendance/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/attendance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/leave/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/leave/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/announcements/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/announcements/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/documents/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/employees/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/employees/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/auth-layout-client.tsx":{"*":{"id":"(rsc)/./src/app/auth/auth-layout-client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/performance/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/performance/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}