import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { logger } from '../utils/logger'
import { DatabaseService } from './databaseService'

const db = new DatabaseService()

export interface TokenPayload {
  userId: string
  email: string
  role: string
  permissions: string[]
  sessionId: string
  deviceFingerprint?: string
  iat?: number
  exp?: number
  jti?: string
  iss?: string
  aud?: string
}

export interface RefreshTokenData {
  id: string
  userId: string
  sessionId: string
  deviceFingerprint: string
  ipAddress: string
  userAgent: string
  expiresAt: Date
  isRevoked: boolean
  createdAt: Date
}

export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: 'Bearer'
}

class JWTService {
  private readonly accessTokenSecret: string
  private readonly refreshTokenSecret: string
  private readonly accessTokenExpiry: string
  private readonly refreshTokenExpiry: string
  private readonly issuer: string
  private readonly audience: string
  private readonly algorithm: jwt.Algorithm

  // Token blacklist for revoked tokens (in production, use Redis)
  private static tokenBlacklist = new Set<string>()

  constructor() {
    this.accessTokenSecret = process.env.JWT_ACCESS_SECRET || 'your-access-secret-key'
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key'
    this.accessTokenExpiry = process.env.JWT_ACCESS_EXPIRY || '15m'
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRY || '7d'
    this.issuer = process.env.JWT_ISSUER || 'peoplenest-api'
    this.audience = process.env.JWT_AUDIENCE || 'peoplenest-client'
    this.algorithm = 'HS256' // In production, use RS256 with public/private key pair

    if (process.env.NODE_ENV === 'production') {
      this.validateProductionConfig()
    }
  }

  /**
   * Generate access and refresh token pair
   */
  async generateTokenPair(
    userId: string,
    email: string,
    role: string,
    permissions: string[],
    sessionId: string,
    deviceFingerprint: string,
    ipAddress: string,
    userAgent: string
  ): Promise<TokenPair> {
    try {
      // Generate unique token ID
      const jti = crypto.randomUUID()

      // Create access token payload
      const accessPayload: TokenPayload = {
        userId,
        email,
        role,
        permissions,
        sessionId,
        deviceFingerprint,
        jti,
        iss: this.issuer,
        aud: this.audience
      }

      // Generate access token
      const accessToken = jwt.sign(accessPayload, this.accessTokenSecret, {
        expiresIn: this.accessTokenExpiry as string,
        algorithm: this.algorithm as jwt.Algorithm,
        issuer: this.issuer,
        audience: this.audience,
        jwtid: jti
      } as jwt.SignOptions)

      // Generate refresh token
      const refreshTokenId = crypto.randomUUID()
      const refreshToken = jwt.sign(
        {
          userId,
          sessionId,
          tokenId: refreshTokenId,
          type: 'refresh',
          iss: this.issuer,
          aud: this.audience
        },
        this.refreshTokenSecret,
        {
          expiresIn: this.refreshTokenExpiry as string,
          algorithm: this.algorithm as jwt.Algorithm,
          issuer: this.issuer,
          audience: this.audience,
          jwtid: refreshTokenId
        } as jwt.SignOptions
      )

      // Store refresh token in database
      await this.storeRefreshToken({
        id: refreshTokenId,
        userId,
        sessionId,
        deviceFingerprint,
        ipAddress,
        userAgent,
        expiresAt: new Date(Date.now() + this.parseExpiry(this.refreshTokenExpiry)),
        isRevoked: false,
        createdAt: new Date()
      })

      // Get expiry time in seconds
      const expiresIn = this.parseExpiry(this.accessTokenExpiry) / 1000

      logger.info('Token pair generated', {
        userId,
        sessionId,
        deviceFingerprint: deviceFingerprint.substring(0, 8) + '...',
        expiresIn
      })

      return {
        accessToken,
        refreshToken,
        expiresIn,
        tokenType: 'Bearer'
      }
    } catch (error) {
      logger.error('Failed to generate token pair:', error)
      throw new Error('Token generation failed')
    }
  }

  /**
   * Verify and decode access token
   */
  async verifyAccessToken(token: string): Promise<TokenPayload> {
    try {
      // Check if token is blacklisted
      if (JWTService.tokenBlacklist.has(token)) {
        throw new Error('Token has been revoked')
      }

      // Verify token
      const decoded = jwt.verify(token, this.accessTokenSecret, {
        algorithms: [this.algorithm],
        issuer: this.issuer,
        audience: this.audience
      }) as TokenPayload

      // Additional security checks
      if (!decoded.userId || !decoded.sessionId) {
        throw new Error('Invalid token payload')
      }

      // Check if session is still valid
      const isSessionValid = await this.validateSession(decoded.sessionId)
      if (!isSessionValid) {
        throw new Error('Session has expired or been invalidated')
      }

      return decoded
    } catch (error) {
      logger.warn('Access token verification failed:', {
        error: error.message,
        token: token.substring(0, 20) + '...'
      })
      throw error
    }
  }

  /**
   * Verify refresh token and generate new access token
   */
  async refreshAccessToken(refreshToken: string, deviceFingerprint: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.refreshTokenSecret, {
        algorithms: [this.algorithm],
        issuer: this.issuer,
        audience: this.audience
      }) as any

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type')
      }

      // Get stored refresh token data
      const storedToken = await this.getRefreshToken(decoded.tokenId)
      if (!storedToken || storedToken.isRevoked) {
        throw new Error('Refresh token has been revoked')
      }

      // Verify device fingerprint
      if (storedToken.deviceFingerprint !== deviceFingerprint) {
        logger.warn('Device fingerprint mismatch during token refresh', {
          userId: storedToken.userId,
          sessionId: storedToken.sessionId,
          storedFingerprint: storedToken.deviceFingerprint.substring(0, 8) + '...',
          providedFingerprint: deviceFingerprint.substring(0, 8) + '...'
        })
        throw new Error('Device fingerprint mismatch')
      }

      // Check if token has expired
      if (storedToken.expiresAt < new Date()) {
        await this.revokeRefreshToken(decoded.tokenId)
        throw new Error('Refresh token has expired')
      }

      // Get user data for new access token
      const user = await this.getUserData(storedToken.userId)
      if (!user) {
        throw new Error('User not found')
      }

      // Generate new token pair
      const newTokenPair = await this.generateTokenPair(
        user.id,
        user.email,
        user.role,
        user.permissions,
        storedToken.sessionId,
        deviceFingerprint,
        storedToken.ipAddress,
        storedToken.userAgent
      )

      // Revoke old refresh token
      await this.revokeRefreshToken(decoded.tokenId)

      logger.info('Access token refreshed', {
        userId: user.id,
        sessionId: storedToken.sessionId
      })

      return newTokenPair
    } catch (error) {
      logger.warn('Token refresh failed:', error.message)
      throw error
    }
  }

  /**
   * Revoke access token (add to blacklist)
   */
  async revokeAccessToken(token: string): Promise<void> {
    try {
      // Add to blacklist
      JWTService.tokenBlacklist.add(token)

      // In production, store in Redis with TTL
      logger.info('Access token revoked')
    } catch (error) {
      logger.error('Failed to revoke access token:', error)
    }
  }

  /**
   * Revoke refresh token
   */
  async revokeRefreshToken(tokenId: string): Promise<void> {
    try {
      await db.query(
        'UPDATE user_sessions SET refresh_token_revoked = true WHERE refresh_token_id = $1',
        [tokenId]
      )

      logger.info('Refresh token revoked', { tokenId })
    } catch (error) {
      logger.error('Failed to revoke refresh token:', error)
    }
  }

  /**
   * Revoke all tokens for a user session
   */
  async revokeAllTokens(sessionId: string): Promise<void> {
    try {
      await db.query(
        'UPDATE user_sessions SET refresh_token_revoked = true WHERE session_id = $1',
        [sessionId]
      )

      logger.info('All tokens revoked for session', { sessionId })
    } catch (error) {
      logger.error('Failed to revoke all tokens:', error)
    }
  }

  /**
   * Store refresh token in database
   */
  private async storeRefreshToken(tokenData: RefreshTokenData): Promise<void> {
    const query = `
      INSERT INTO user_sessions (
        refresh_token_id, user_id, session_id, device_fingerprint,
        ip_address, user_agent, expires_at, refresh_token_revoked, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (session_id) 
      DO UPDATE SET 
        refresh_token_id = $1,
        device_fingerprint = $4,
        ip_address = $5,
        user_agent = $6,
        expires_at = $7,
        refresh_token_revoked = $8,
        updated_at = NOW()
    `

    await db.query(query, [
      tokenData.id,
      tokenData.userId,
      tokenData.sessionId,
      tokenData.deviceFingerprint,
      tokenData.ipAddress,
      tokenData.userAgent,
      tokenData.expiresAt,
      tokenData.isRevoked,
      tokenData.createdAt
    ])
  }

  /**
   * Get refresh token from database
   */
  private async getRefreshToken(tokenId: string): Promise<RefreshTokenData | null> {
    const result = await db.query(
      'SELECT * FROM user_sessions WHERE refresh_token_id = $1',
      [tokenId]
    )

    if (result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.refresh_token_id,
      userId: row.user_id,
      sessionId: row.session_id,
      deviceFingerprint: row.device_fingerprint,
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      expiresAt: row.expires_at,
      isRevoked: row.refresh_token_revoked,
      createdAt: row.created_at
    }
  }

  /**
   * Validate session
   */
  private async validateSession(sessionId: string): Promise<boolean> {
    const result = await db.query(
      'SELECT 1 FROM user_sessions WHERE session_id = $1 AND refresh_token_revoked = false',
      [sessionId]
    )

    return result.rows.length > 0
  }

  /**
   * Get user data for token generation
   */
  private async getUserData(userId: string): Promise<any> {
    const result = await db.query(
      'SELECT id, email, role FROM users WHERE id = $1 AND is_active = true',
      [userId]
    )

    if (result.rows.length === 0) {
      return null
    }

    const user = result.rows[0]
    
    // Get user permissions (implement based on your RBAC system)
    const permissions = await this.getUserPermissions(user.role)

    return {
      ...user,
      permissions
    }
  }

  /**
   * Get user permissions based on role
   */
  private async getUserPermissions(role: string): Promise<string[]> {
    // This should match your existing permission system
    const rolePermissions: Record<string, string[]> = {
      'super_admin': [
        'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
      ],
      'hr_admin': [
        'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
      ],
      'hr': [
        'hr', 'employee', 'recruiter'
      ],
      'manager': [
        'manager', 'employee'
      ],
      'employee': [
        'employee'
      ]
    }

    return rolePermissions[role] || ['employee']
  }

  /**
   * Parse expiry string to milliseconds
   */
  private parseExpiry(expiry: string): number {
    const unit = expiry.slice(-1)
    const value = parseInt(expiry.slice(0, -1))

    switch (unit) {
      case 's': return value * 1000
      case 'm': return value * 60 * 1000
      case 'h': return value * 60 * 60 * 1000
      case 'd': return value * 24 * 60 * 60 * 1000
      default: return 15 * 60 * 1000 // Default 15 minutes
    }
  }

  /**
   * Validate production configuration
   */
  private validateProductionConfig(): void {
    const requiredEnvVars = [
      'JWT_ACCESS_SECRET',
      'JWT_REFRESH_SECRET',
      'JWT_ISSUER',
      'JWT_AUDIENCE'
    ]

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`)
      }
    }

    // Check secret strength
    if (this.accessTokenSecret.length < 32) {
      throw new Error('JWT_ACCESS_SECRET must be at least 32 characters long')
    }

    if (this.refreshTokenSecret.length < 32) {
      throw new Error('JWT_REFRESH_SECRET must be at least 32 characters long')
    }
  }

  /**
   * Generate device fingerprint
   */
  static generateDeviceFingerprint(userAgent: string, acceptLanguage: string = '', acceptEncoding: string = ''): string {
    const data = `${userAgent}|${acceptLanguage}|${acceptEncoding}`
    return crypto.createHash('sha256').update(data).digest('hex')
  }

  /**
   * Clean up expired tokens (should be run as a cron job)
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await db.query(
        'DELETE FROM user_sessions WHERE expires_at < NOW() OR refresh_token_revoked = true'
      )

      logger.info(`Cleaned up ${result.rowCount} expired/revoked tokens`)
    } catch (error) {
      logger.error('Failed to cleanup expired tokens:', error)
    }
  }
}

export const jwtService = new JWTService()
export { JWTService }
export default jwtService
