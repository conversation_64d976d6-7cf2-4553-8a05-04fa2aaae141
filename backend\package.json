{"name": "peoplenest-backend", "version": "1.0.0", "description": "PeopleNest HRMS Backend API", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit src/tests/services", "test:integration": "jest tests/integration src/tests/routes", "test:e2e": "jest tests/e2e", "test:performance": "jest tests/performance", "test:ai": "jest tests/unit/services/aiService.test.ts tests/unit/services/aiProviderService.test.ts tests/integration/routes/ai.test.ts", "test:auth": "jest src/tests/services/auditService.test.ts src/tests/services/mfaService.test.ts src/tests/routes/auth.integration.test.ts", "test:security": "jest --testNamePattern='Security|security|MFA|mfa|audit|Audit'", "test:ci": "jest --ci --coverage --watchAll=false --maxWorkers=2", "test:verbose": "jest --verbose --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "ts-node src/database/migrationRunner.ts migrate", "db:migrate:status": "ts-node src/database/migrationRunner.ts status", "db:migrate:rollback": "ts-node src/database/migrationRunner.ts rollback", "db:seed": "ts-node src/database/seed.ts seed", "db:setup": "ts-node src/database/setup.ts setup", "db:test": "ts-node src/database/setup.ts test", "db:status": "ts-node src/database/setup.ts status", "db:reset": "ts-node src/database/setup.ts reset"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^5.6.0", "@types/bcrypt": "^5.0.2", "@types/express-session": "^1.18.2", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "axios": "^1.6.2", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mammoth": "^1.6.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "prisma": "^5.6.0", "qrcode": "^1.5.4", "react-hook-form": "^7.58.1", "redis": "^4.6.10", "sharp": "^0.32.6", "speakeasy": "^2.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["hrms", "employee-management", "payroll", "performance", "api", "typescript", "express"], "author": "PeopleNest Team", "license": "MIT"}