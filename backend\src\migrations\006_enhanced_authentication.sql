-- Migration 006: Enhanced Authentication System
-- Adds support for enhanced JWT service with device fingerprinting and improved session management

-- Add new columns to user_sessions table for enhanced authentication
ALTER TABLE user_sessions 
ADD COLUMN IF NOT EXISTS refresh_token_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS device_fingerprint VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS refresh_token_revoked BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing sessions to have default values
UPDATE user_sessions 
SET 
  refresh_token_revoked = false,
  last_used = COALESCE(last_activity, created_at),
  updated_at = NOW()
WHERE refresh_token_revoked IS NULL;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_user_sessions_refresh_token_id ON user_sessions(refresh_token_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_fingerprint ON user_sessions(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_user_sessions_revoked ON user_sessions(refresh_token_revoked);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_used ON user_sessions(last_used);

-- Add constraint to ensure refresh_token_id is unique when not null
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_sessions_refresh_token_id_unique 
ON user_sessions(refresh_token_id) 
WHERE refresh_token_id IS NOT NULL;

-- Create audit_logs table for comprehensive audit trail
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(255) NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT')),
    old_values JSONB,
    new_values JSONB,
    user_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    data_classification VARCHAR(20) DEFAULT 'INTERNAL' CHECK (data_classification IN ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED')),
    legal_basis TEXT,
    purpose TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance
    CONSTRAINT audit_logs_action_check CHECK (action IN ('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT')),
    CONSTRAINT audit_logs_classification_check CHECK (data_classification IN ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED'))
);

-- Create data_access_logs table for GDPR compliance
CREATE TABLE IF NOT EXISTS data_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    employee_id VARCHAR(255) NOT NULL,
    data_type VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    purpose TEXT NOT NULL,
    legal_basis VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    retention_date TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create indexes for audit_logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_classification ON audit_logs(data_classification);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);

-- Create indexes for data_access_logs
CREATE INDEX IF NOT EXISTS idx_data_access_logs_user_id ON data_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_employee_id ON data_access_logs(employee_id);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_timestamp ON data_access_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_retention ON data_access_logs(retention_date);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_data_type ON data_access_logs(data_type);

-- Add last_activity column to users table if it doesn't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for user_sessions updated_at
DROP TRIGGER IF EXISTS update_user_sessions_updated_at ON user_sessions;
CREATE TRIGGER update_user_sessions_updated_at
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for users updated_at (if not exists)
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all system operations with GDPR compliance';
COMMENT ON TABLE data_access_logs IS 'GDPR-compliant logging of personal data access with retention policies';
COMMENT ON COLUMN user_sessions.refresh_token_id IS 'Unique identifier for refresh tokens';
COMMENT ON COLUMN user_sessions.device_fingerprint IS 'Device fingerprint for security tracking';
COMMENT ON COLUMN user_sessions.refresh_token_revoked IS 'Flag indicating if refresh token has been revoked';
COMMENT ON COLUMN audit_logs.data_classification IS 'Data classification level for compliance (PUBLIC, INTERNAL, CONFIDENTIAL, RESTRICTED)';
COMMENT ON COLUMN audit_logs.legal_basis IS 'Legal basis for data processing under GDPR';
COMMENT ON COLUMN data_access_logs.retention_date IS 'Date when this log entry should be deleted for compliance';

-- Create view for active sessions
CREATE OR REPLACE VIEW active_user_sessions AS
SELECT 
    us.*,
    u.email,
    u.role,
    e.first_name_encrypted,
    e.last_name_encrypted
FROM user_sessions us
JOIN users u ON us.user_id = u.id
LEFT JOIN employees e ON u.employee_id = e.id
WHERE us.is_active = true 
  AND us.expires_at > NOW()
  AND (us.refresh_token_revoked IS NULL OR us.refresh_token_revoked = false);

-- Grant appropriate permissions
GRANT SELECT ON active_user_sessions TO PUBLIC;

-- Create function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired sessions
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() - INTERVAL '7 days'
       OR (refresh_token_revoked = true AND updated_at < NOW() - INTERVAL '1 day');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup operation
    INSERT INTO audit_logs (
        table_name, record_id, action, new_values,
        data_classification, purpose, timestamp
    ) VALUES (
        'user_sessions', 'cleanup', 'DELETE',
        jsonb_build_object('deleted_count', deleted_count),
        'INTERNAL', 'Automated cleanup of expired sessions', NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up expired audit logs
CREATE OR REPLACE FUNCTION cleanup_expired_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired data access logs based on retention_date
    DELETE FROM data_access_logs 
    WHERE retention_date < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Keep audit_logs for 7 years (regulatory requirement)
    DELETE FROM audit_logs 
    WHERE timestamp < NOW() - INTERVAL '7 years';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Insert default data retention policies
INSERT INTO data_retention_policies (data_type, retention_period_days, legal_basis, description)
VALUES 
    ('audit_logs', 2555, 'Legal Obligation', 'Audit logs retained for 7 years for regulatory compliance'),
    ('session_data', 90, 'Legitimate Interest', 'Session data retained for 90 days for security monitoring'),
    ('authentication_logs', 365, 'Legitimate Interest', 'Authentication logs retained for 1 year for security analysis')
ON CONFLICT (data_type) DO NOTHING;

-- Migration completion log
INSERT INTO audit_logs (
    table_name, record_id, action, new_values,
    data_classification, purpose, timestamp
) VALUES (
    'migrations', '006_enhanced_authentication', 'CREATE',
    jsonb_build_object(
        'migration', '006_enhanced_authentication.sql',
        'description', 'Enhanced authentication system with JWT service support',
        'features', jsonb_build_array(
            'Enhanced user_sessions table',
            'Comprehensive audit logging',
            'GDPR-compliant data access logging',
            'Automated cleanup functions',
            'Device fingerprinting support'
        )
    ),
    'INTERNAL', 'Database migration', NOW()
);
