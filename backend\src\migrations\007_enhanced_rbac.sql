-- Enhanced RBAC Migration for PeopleNest
-- This migration enhances the existing RBAC system with:
-- 1. Scope-based permissions (own, team, department, all)
-- 2. Conditional access policies
-- 3. Enhanced role management
-- 4. Temporal role assignments
-- 5. Improved audit logging

-- Add scope column to permissions table
ALTER TABLE permissions 
ADD COLUMN IF NOT EXISTS scope VARCHAR(20) DEFAULT 'all',
ADD COLUMN IF NOT EXISTS requires_mfa BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_sensitive BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS conditions JSONB DEFAULT '{}';

-- Create index on scope for better performance
CREATE INDEX IF NOT EXISTS idx_permissions_scope ON permissions(scope);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);

-- Create enhanced roles table (separate from enum for flexibility)
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    max_concurrent_users INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Insert system roles
INSERT INTO roles (name, display_name, description, is_system_role, is_active) VALUES
('super_admin', 'Super Administrator', 'Full system access with all administrative privileges', true, true),
('hr_admin', 'HR Administrator', 'Complete HR management with administrative capabilities', true, true),
('hr_manager', 'HR Manager', 'HR operations management with team oversight', true, true),
('manager', 'Manager', 'Team management with employee oversight', true, true),
('employee', 'Employee', 'Standard employee access to personal data and basic functions', true, true)
ON CONFLICT (name) DO NOTHING;

-- Create user role assignments table for temporal and delegated roles
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_name VARCHAR(50) NOT NULL,
    
    -- Temporal assignment
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    
    -- Assignment context
    assigned_by UUID REFERENCES users(id),
    assignment_reason TEXT,
    
    -- Delegation support
    is_delegated BOOLEAN DEFAULT false,
    delegated_by UUID REFERENCES users(id),
    delegation_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_assignment_period CHECK (expires_at IS NULL OR expires_at > assigned_at),
    CONSTRAINT valid_delegation CHECK (
        (is_delegated = false) OR
        (is_delegated = true AND delegated_by IS NOT NULL AND delegation_expires_at IS NOT NULL)
    )
);

-- Create indexes for user role assignments
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_user_id ON user_role_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_role_name ON user_role_assignments(role_name);
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_active ON user_role_assignments(is_active);
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_expires ON user_role_assignments(expires_at);

-- Enhanced role permissions table with conditions
ALTER TABLE role_permissions 
ADD COLUMN IF NOT EXISTS conditions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS granted_by UUID REFERENCES users(id);

-- Create role audit log
CREATE TABLE IF NOT EXISTS role_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    role_name VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL, -- 'granted', 'revoked', 'expired', 'delegated'
    previous_state JSONB,
    new_state JSONB,
    performed_by UUID REFERENCES users(id),
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    reason TEXT
);

-- Create indexes for role audit log
CREATE INDEX IF NOT EXISTS idx_role_audit_log_user_id ON role_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_role_audit_log_performed_at ON role_audit_log(performed_at);
CREATE INDEX IF NOT EXISTS idx_role_audit_log_action ON role_audit_log(action);

-- Insert enhanced permissions with scope
INSERT INTO permissions (name, resource, action, scope, description, requires_mfa, is_sensitive) VALUES
-- Employee permissions with scope
('employees:read:own', 'employees', 'read', 'own', 'Read own employee data', false, false),
('employees:read:team', 'employees', 'read', 'team', 'Read team employee data', false, false),
('employees:read:department', 'employees', 'read', 'department', 'Read department employee data', false, false),
('employees:read:all', 'employees', 'read', 'all', 'Read all employee data', false, false),
('employees:update:own', 'employees', 'update', 'own', 'Update own employee data', false, false),
('employees:update:team', 'employees', 'update', 'team', 'Update team employee data', false, false),
('employees:update:all', 'employees', 'update', 'all', 'Update all employee data', false, false),
('employees:create:all', 'employees', 'create', 'all', 'Create new employees', false, false),
('employees:delete:all', 'employees', 'delete', 'all', 'Delete employees', true, true),

-- Payroll permissions
('payroll:read:own', 'payroll', 'read', 'own', 'Read own payroll data', false, true),
('payroll:read:team', 'payroll', 'read', 'team', 'Read team payroll data', true, true),
('payroll:read:all', 'payroll', 'read', 'all', 'Read all payroll data', true, true),
('payroll:process:all', 'payroll', 'process', 'all', 'Process payroll', true, true),
('payroll:approve:all', 'payroll', 'approve', 'all', 'Approve payroll', true, true),

-- Performance permissions
('performance:read:own', 'performance', 'read', 'own', 'Read own performance data', false, false),
('performance:read:team', 'performance', 'read', 'team', 'Read team performance data', false, false),
('performance:read:all', 'performance', 'read', 'all', 'Read all performance data', false, false),
('performance:update:team', 'performance', 'update', 'team', 'Update team performance data', false, false),
('performance:update:all', 'performance', 'update', 'all', 'Update all performance data', false, false),

-- Leave permissions
('leave:read:own', 'leave', 'read', 'own', 'Read own leave data', false, false),
('leave:read:team', 'leave', 'read', 'team', 'Read team leave data', false, false),
('leave:read:all', 'leave', 'read', 'all', 'Read all leave data', false, false),
('leave:approve:team', 'leave', 'approve', 'team', 'Approve team leave requests', false, false),
('leave:approve:all', 'leave', 'approve', 'all', 'Approve all leave requests', false, false),

-- System permissions
('system:admin:all', 'system', 'admin', 'all', 'System administration', true, true),
('users:manage:all', 'users', 'manage', 'all', 'Manage user accounts', true, true),
('audit:read:all', 'audit', 'read', 'all', 'Read audit logs', true, true),
('reports:create:all', 'reports', 'create', 'all', 'Create reports', false, false),
('reports:export:all', 'reports', 'export', 'all', 'Export reports', false, true)

ON CONFLICT (name) DO NOTHING;

-- Update role permissions with new scoped permissions
-- Clear existing role permissions for clean setup
DELETE FROM role_permissions WHERE role IN ('super_admin', 'hr_admin', 'hr_manager', 'manager', 'employee');

-- Super Admin - all permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'super_admin', id FROM permissions;

-- HR Admin permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'hr_admin', id FROM permissions 
WHERE name IN (
    'employees:read:all', 'employees:update:all', 'employees:create:all',
    'payroll:read:all', 'payroll:process:all', 'payroll:approve:all',
    'performance:read:all', 'performance:update:all',
    'leave:read:all', 'leave:approve:all',
    'reports:create:all', 'reports:export:all',
    'users:manage:all', 'audit:read:all'
);

-- HR Manager permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'hr_manager', id FROM permissions 
WHERE name IN (
    'employees:read:all', 'employees:update:all', 'employees:create:all',
    'payroll:read:all',
    'performance:read:all', 'performance:update:all',
    'leave:read:all', 'leave:approve:all',
    'reports:create:all'
);

-- Manager permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'manager', id FROM permissions 
WHERE name IN (
    'employees:read:own', 'employees:read:team', 'employees:update:own', 'employees:update:team',
    'payroll:read:own',
    'performance:read:own', 'performance:read:team', 'performance:update:team',
    'leave:read:own', 'leave:read:team', 'leave:approve:team'
);

-- Employee permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'employee', id FROM permissions 
WHERE name IN (
    'employees:read:own', 'employees:update:own',
    'payroll:read:own',
    'performance:read:own',
    'leave:read:own'
);

-- Add MFA completion tracking to user_sessions
ALTER TABLE user_sessions 
ADD COLUMN IF NOT EXISTS mfa_completed BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS mfa_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS risk_score INTEGER DEFAULT 0;

-- Create function to automatically assign default role to new users
CREATE OR REPLACE FUNCTION assign_default_role()
RETURNS TRIGGER AS $$
BEGIN
    -- Assign default employee role to new users
    INSERT INTO user_role_assignments (user_id, role_name, assigned_by, assignment_reason)
    VALUES (NEW.id, NEW.role, NEW.id, 'Default role assignment');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic role assignment
DROP TRIGGER IF EXISTS trigger_assign_default_role ON users;
CREATE TRIGGER trigger_assign_default_role
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION assign_default_role();

-- Create function to clean up expired role assignments
CREATE OR REPLACE FUNCTION cleanup_expired_role_assignments()
RETURNS void AS $$
BEGIN
    -- Mark expired assignments as inactive
    UPDATE user_role_assignments 
    SET is_active = false, updated_at = NOW()
    WHERE expires_at < NOW() AND is_active = true;
    
    -- Mark expired delegations as inactive
    UPDATE user_role_assignments 
    SET is_active = false, updated_at = NOW()
    WHERE delegation_expires_at < NOW() AND is_delegated = true AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_permissions_requires_mfa ON permissions(requires_mfa);
CREATE INDEX IF NOT EXISTS idx_permissions_is_sensitive ON permissions(is_sensitive);
CREATE INDEX IF NOT EXISTS idx_role_permissions_active ON role_permissions(is_active);

-- Update existing users to have role assignments
INSERT INTO user_role_assignments (user_id, role_name, assigned_by, assignment_reason)
SELECT id, role, id, 'Migration assignment'
FROM users 
WHERE id NOT IN (SELECT user_id FROM user_role_assignments)
ON CONFLICT DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE permissions IS 'System permissions with scope-based access control';
COMMENT ON TABLE roles IS 'Role definitions with metadata and constraints';
COMMENT ON TABLE user_role_assignments IS 'Temporal and delegated role assignments for users';
COMMENT ON TABLE role_audit_log IS 'Audit trail for all role-related changes';
COMMENT ON COLUMN permissions.scope IS 'Access scope: own, team, department, all';
COMMENT ON COLUMN permissions.requires_mfa IS 'Whether this permission requires MFA completion';
COMMENT ON COLUMN permissions.is_sensitive IS 'Whether this permission involves sensitive data';
