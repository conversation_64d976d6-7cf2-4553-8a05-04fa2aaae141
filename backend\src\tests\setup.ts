import { DatabaseService } from '../services/databaseService'

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test'
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
  process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-for-testing-only'
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/peoplenest_test'
  
  // Set up test database if needed
  // In a real implementation, you might want to set up a test database
  console.log('Setting up test environment...')
})

afterAll(async () => {
  // Clean up after all tests
  console.log('Cleaning up test environment...')
})

// Mock console methods to reduce noise during testing
const originalConsole = { ...console }

beforeEach(() => {
  // Reset console mocks before each test
  console.log = jest.fn()
  console.warn = jest.fn()
  console.error = jest.fn()
})

afterEach(() => {
  // Restore console after each test if needed for debugging
  if (process.env.DEBUG_TESTS) {
    console.log = originalConsole.log
    console.warn = originalConsole.warn
    console.error = originalConsole.error
  }
})

// Global test utilities
export const testUtils = {
  // Create a mock user for testing
  createMockUser: (overrides = {}) => ({
    id: 'test-user-123',
    email: '<EMAIL>',
    password_hash: '$2b$10$hashedpassword',
    role: 'employee',
    is_active: true,
    failed_login_attempts: 0,
    locked_until: null,
    created_at: new Date(),
    updated_at: new Date(),
    ...overrides
  }),

  // Create mock tokens
  createMockTokens: () => ({
    accessToken: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXVzZXItMTIzIiwicm9sZSI6ImVtcGxveWVlIiwiaWF0IjoxNjAwMDAwMDAwLCJleHAiOjE2MDAwMDM2MDB9.signature',
    refreshToken: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXVzZXItMTIzIiwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE2MDAwMDAwMDAsImV4cCI6MTYwMDAwMzYwMH0.signature'
  }),

  // Create mock session
  createMockSession: (overrides = {}) => ({
    id: 'test-session-123',
    userId: 'test-user-123',
    deviceFingerprint: 'test-device-123',
    ipAddress: '127.0.0.1',
    userAgent: 'Test User Agent',
    isActive: true,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),

  // Create mock audit event
  createMockAuditEvent: (overrides = {}) => ({
    userId: 'test-user-123',
    sessionId: 'test-session-123',
    eventType: 'test_event',
    eventCategory: 'system_event' as const,
    resource: 'test',
    action: 'test',
    details: {},
    ipAddress: '127.0.0.1',
    userAgent: 'Test User Agent',
    timestamp: new Date(),
    severity: 'medium' as const,
    success: true,
    riskScore: 30,
    ...overrides
  }),

  // Wait for async operations
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Generate random test data
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  randomEmail: () => `test${Math.random().toString(36).substr(2, 9)}@example.com`,

  randomUUID: () => `${Math.random().toString(36).substr(2, 8)}-${Math.random().toString(36).substr(2, 4)}-${Math.random().toString(36).substr(2, 4)}-${Math.random().toString(36).substr(2, 4)}-${Math.random().toString(36).substr(2, 12)}`,

  // Database test helpers
  mockDbQuery: (mockDb: any, returnValue: any) => {
    mockDb.query.mockResolvedValueOnce(returnValue)
  },

  mockDbQueryError: (mockDb: any, error: Error) => {
    mockDb.query.mockRejectedValueOnce(error)
  },

  // Validation helpers
  expectValidationError: (response: any, field: string) => {
    expect(response.body).toMatchObject({
      error: 'Validation Error',
      details: expect.arrayContaining([
        expect.objectContaining({
          param: field
        })
      ])
    })
  },

  // Security test helpers
  createSQLInjectionPayload: () => "'; DROP TABLE users; --",
  createXSSPayload: () => '<script>alert("xss")</script>',
  createLongString: (length = 10000) => 'A'.repeat(length),

  // Time helpers
  futureDate: (hours = 1) => new Date(Date.now() + hours * 60 * 60 * 1000),
  pastDate: (hours = 1) => new Date(Date.now() - hours * 60 * 60 * 1000),

  // Mock request helpers
  createMockRequest: (overrides = {}) => ({
    ip: '127.0.0.1',
    get: jest.fn().mockReturnValue('Test User Agent'),
    user: testUtils.createMockUser(),
    sessionID: 'test-session-123',
    headers: {
      'user-agent': 'Test User Agent',
      'x-request-id': 'test-request-123'
    },
    body: {},
    params: {},
    query: {},
    path: '/test',
    method: 'GET',
    ...overrides
  }),

  createMockResponse: () => {
    const res: any = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis(),
      statusCode: 200
    }
    return res
  }
}

// Export for use in tests
export default testUtils
