import * as speakeasy from 'speakeasy'
import * as QRCode from 'qrcode'
import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'
import { AuditService } from './auditService'
import crypto from 'crypto'

export interface MFASecret {
  secret: string
  qrCodeUrl: string
  backupCodes: string[]
  manualEntryKey: string
}

export interface MFAVerificationResult {
  verified: boolean
  backupCodeUsed?: boolean
  remainingBackupCodes?: number
}

export interface MFAStatus {
  enabled: boolean
  verified: boolean
  backupCodesRemaining: number
  lastUsed?: Date
  setupDate?: Date
}

export class MFAService {
  private db: DatabaseService
  private auditService: AuditService

  constructor() {
    this.db = new DatabaseService()
    this.auditService = new AuditService()
  }

  /**
   * Generate MFA secret and QR code for user setup
   */
  async generateMFASecret(userId: string, userEmail: string): Promise<MFASecret> {
    try {
      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `PeopleNest (${userEmail})`,
        issuer: 'PeopleNest HRMS',
        length: 32
      })

      // Generate backup codes
      const backupCodes = this.generateBackupCodes()

      // Create QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!)

      // Store temporary MFA setup in database
      await this.db.query(`
        INSERT INTO user_mfa_setup (user_id, secret_encrypted, backup_codes_encrypted, created_at, expires_at)
        VALUES ($1, pgp_sym_encrypt($2, $3), pgp_sym_encrypt($4, $3), NOW(), NOW() + INTERVAL '15 minutes')
        ON CONFLICT (user_id) 
        DO UPDATE SET 
          secret_encrypted = pgp_sym_encrypt($2, $3),
          backup_codes_encrypted = pgp_sym_encrypt($4, $3),
          created_at = NOW(),
          expires_at = NOW() + INTERVAL '15 minutes'
      `, [
        userId,
        secret.base32,
        JSON.stringify(backupCodes),
        process.env.ENCRYPTION_KEY || 'default-key'
      ])

      logger.info('MFA secret generated for user', { userId, userEmail })

      return {
        secret: secret.base32,
        qrCodeUrl,
        backupCodes,
        manualEntryKey: secret.base32
      }

    } catch (error) {
      logger.error('Failed to generate MFA secret:', error)
      throw new Error('Failed to generate MFA secret')
    }
  }

  /**
   * Verify MFA token and complete setup
   */
  async verifyAndEnableMFA(userId: string, token: string, sessionId?: string): Promise<boolean> {
    try {
      // Get temporary setup data
      const setupResult = await this.db.query(`
        SELECT pgp_sym_decrypt(secret_encrypted, $2) as secret,
               pgp_sym_decrypt(backup_codes_encrypted, $2) as backup_codes
        FROM user_mfa_setup 
        WHERE user_id = $1 AND expires_at > NOW()
      `, [userId, process.env.ENCRYPTION_KEY || 'default-key'])

      if (setupResult.rows.length === 0) {
        throw new Error('MFA setup not found or expired')
      }

      const { secret, backup_codes } = setupResult.rows[0]

      // Verify token
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token,
        window: 2 // Allow 2 time steps (60 seconds) tolerance
      })

      if (!verified) {
        await this.auditService.logSecurityEvent({
          userId,
          sessionId,
          eventType: 'mfa_verification_failed',
          details: { reason: 'invalid_token' },
          ipAddress: '',
          userAgent: '',
          timestamp: new Date(),
          severity: 'medium'
        })
        return false
      }

      // Enable MFA for user
      await this.db.query(`
        INSERT INTO user_mfa (user_id, secret_encrypted, backup_codes_encrypted, enabled, verified, setup_date)
        VALUES ($1, pgp_sym_encrypt($2, $3), pgp_sym_encrypt($4, $3), true, true, NOW())
        ON CONFLICT (user_id)
        DO UPDATE SET
          secret_encrypted = pgp_sym_encrypt($2, $3),
          backup_codes_encrypted = pgp_sym_encrypt($4, $3),
          enabled = true,
          verified = true,
          setup_date = NOW()
      `, [
        userId,
        secret,
        backup_codes,
        process.env.ENCRYPTION_KEY || 'default-key'
      ])

      // Clean up temporary setup
      await this.db.query('DELETE FROM user_mfa_setup WHERE user_id = $1', [userId])

      // Update user session to mark MFA as completed
      if (sessionId) {
        await this.db.query(`
          UPDATE user_sessions 
          SET mfa_completed = true, mfa_completed_at = NOW()
          WHERE session_id = $1
        `, [sessionId])
      }

      await this.auditService.logSecurityEvent({
        userId,
        sessionId,
        eventType: 'mfa_enabled',
        details: { method: 'totp' },
        ipAddress: '',
        userAgent: '',
        timestamp: new Date(),
        severity: 'low'
      })

      logger.info('MFA enabled for user', { userId })
      return true

    } catch (error) {
      logger.error('Failed to verify and enable MFA:', error)
      throw error
    }
  }

  /**
   * Verify MFA token for authentication
   */
  async verifyMFAToken(userId: string, token: string, sessionId?: string): Promise<MFAVerificationResult> {
    try {
      const mfaResult = await this.db.query(`
        SELECT pgp_sym_decrypt(secret_encrypted, $2) as secret,
               pgp_sym_decrypt(backup_codes_encrypted, $2) as backup_codes,
               enabled, verified
        FROM user_mfa 
        WHERE user_id = $1 AND enabled = true
      `, [userId, process.env.ENCRYPTION_KEY || 'default-key'])

      if (mfaResult.rows.length === 0) {
        throw new Error('MFA not enabled for user')
      }

      const { secret, backup_codes, enabled, verified } = mfaResult.rows[0]

      if (!enabled || !verified) {
        throw new Error('MFA not properly configured')
      }

      // First try TOTP verification
      const totpVerified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token,
        window: 2
      })

      if (totpVerified) {
        // Update last used timestamp
        await this.db.query(`
          UPDATE user_mfa SET last_used = NOW() WHERE user_id = $1
        `, [userId])

        // Mark session as MFA completed
        if (sessionId) {
          await this.db.query(`
            UPDATE user_sessions 
            SET mfa_completed = true, mfa_completed_at = NOW()
            WHERE session_id = $1
          `, [sessionId])
        }

        await this.auditService.logSecurityEvent({
          userId,
          sessionId,
          eventType: 'mfa_verification_success',
          details: { method: 'totp' },
          ipAddress: '',
          userAgent: '',
          timestamp: new Date(),
          severity: 'low'
        })

        return { verified: true }
      }

      // Try backup code verification
      const backupCodesList: string[] = JSON.parse(backup_codes || '[]')
      const codeIndex = backupCodesList.indexOf(token.replace(/\s/g, ''))

      if (codeIndex !== -1) {
        // Remove used backup code
        backupCodesList.splice(codeIndex, 1)

        // Update backup codes in database
        await this.db.query(`
          UPDATE user_mfa 
          SET backup_codes_encrypted = pgp_sym_encrypt($2, $3),
              last_used = NOW()
          WHERE user_id = $1
        `, [
          userId,
          JSON.stringify(backupCodesList),
          process.env.ENCRYPTION_KEY || 'default-key'
        ])

        // Mark session as MFA completed
        if (sessionId) {
          await this.db.query(`
            UPDATE user_sessions 
            SET mfa_completed = true, mfa_completed_at = NOW()
            WHERE session_id = $1
          `, [sessionId])
        }

        await this.auditService.logSecurityEvent({
          userId,
          sessionId,
          eventType: 'mfa_verification_success',
          details: { method: 'backup_code', remainingCodes: backupCodesList.length },
          ipAddress: '',
          userAgent: '',
          timestamp: new Date(),
          severity: 'low'
        })

        return {
          verified: true,
          backupCodeUsed: true,
          remainingBackupCodes: backupCodesList.length
        }
      }

      // Verification failed
      await this.auditService.logSecurityEvent({
        userId,
        sessionId,
        eventType: 'mfa_verification_failed',
        details: { reason: 'invalid_token_and_backup_code' },
        ipAddress: '',
        userAgent: '',
        timestamp: new Date(),
        severity: 'medium'
      })

      return { verified: false }

    } catch (error) {
      logger.error('Failed to verify MFA token:', error)
      throw error
    }
  }

  /**
   * Get MFA status for user
   */
  async getMFAStatus(userId: string): Promise<MFAStatus> {
    try {
      const result = await this.db.query(`
        SELECT enabled, verified, last_used, setup_date,
               pgp_sym_decrypt(backup_codes_encrypted, $2) as backup_codes
        FROM user_mfa 
        WHERE user_id = $1
      `, [userId, process.env.ENCRYPTION_KEY || 'default-key'])

      if (result.rows.length === 0) {
        return {
          enabled: false,
          verified: false,
          backupCodesRemaining: 0
        }
      }

      const { enabled, verified, last_used, setup_date, backup_codes } = result.rows[0]
      const backupCodesList: string[] = JSON.parse(backup_codes || '[]')

      return {
        enabled,
        verified,
        backupCodesRemaining: backupCodesList.length,
        lastUsed: last_used,
        setupDate: setup_date
      }

    } catch (error) {
      logger.error('Failed to get MFA status:', error)
      throw error
    }
  }

  /**
   * Disable MFA for user
   */
  async disableMFA(userId: string, adminUserId?: string): Promise<boolean> {
    try {
      await this.db.query(`
        UPDATE user_mfa 
        SET enabled = false, disabled_at = NOW(), disabled_by = $2
        WHERE user_id = $1
      `, [userId, adminUserId])

      // Clear MFA completion from active sessions
      await this.db.query(`
        UPDATE user_sessions 
        SET mfa_completed = false, mfa_completed_at = NULL
        WHERE user_id = $1 AND expires_at > NOW()
      `, [userId])

      await this.auditService.logSecurityEvent({
        userId,
        eventType: 'mfa_disabled',
        details: { disabledBy: adminUserId },
        ipAddress: '',
        userAgent: '',
        timestamp: new Date(),
        severity: 'medium'
      })

      logger.info('MFA disabled for user', { userId, disabledBy: adminUserId })
      return true

    } catch (error) {
      logger.error('Failed to disable MFA:', error)
      throw error
    }
  }

  /**
   * Generate new backup codes
   */
  async regenerateBackupCodes(userId: string): Promise<string[]> {
    try {
      const newBackupCodes = this.generateBackupCodes()

      await this.db.query(`
        UPDATE user_mfa 
        SET backup_codes_encrypted = pgp_sym_encrypt($2, $3)
        WHERE user_id = $1 AND enabled = true
      `, [
        userId,
        JSON.stringify(newBackupCodes),
        process.env.ENCRYPTION_KEY || 'default-key'
      ])

      await this.auditService.logSecurityEvent({
        userId,
        eventType: 'mfa_backup_codes_regenerated',
        details: { codeCount: newBackupCodes.length },
        ipAddress: '',
        userAgent: '',
        timestamp: new Date(),
        severity: 'low'
      })

      logger.info('Backup codes regenerated for user', { userId })
      return newBackupCodes

    } catch (error) {
      logger.error('Failed to regenerate backup codes:', error)
      throw error
    }
  }

  /**
   * Check if MFA is required for user based on role and policies
   */
  async isMFARequired(userId: string): Promise<boolean> {
    try {
      const result = await this.db.query(`
        SELECT u.role, p.requires_mfa
        FROM users u
        LEFT JOIN role_permissions rp ON u.role = rp.role
        LEFT JOIN permissions p ON rp.permission_id = p.id
        WHERE u.id = $1 AND p.requires_mfa = true
        LIMIT 1
      `, [userId])

      // MFA required for admin roles or if user has any permission requiring MFA
      return result.rows.length > 0

    } catch (error) {
      logger.error('Failed to check MFA requirement:', error)
      return false
    }
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = []
    for (let i = 0; i < 10; i++) {
      const code = crypto.randomBytes(4).toString('hex').toUpperCase()
      codes.push(`${code.slice(0, 4)}-${code.slice(4, 8)}`)
    }
    return codes
  }
}

// Export singleton instance
export const mfaService = new MFAService()
