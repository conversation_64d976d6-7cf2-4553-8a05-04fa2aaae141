import { logger } from '../utils/logger'
import { auditLogger } from '../middleware/audit'
import { DatabaseService } from './databaseService'

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
  scope?: string
  description?: string
  requiresMfa?: boolean
  isSensitive?: boolean
}

export interface Role {
  id: string
  name: string
  displayName: string
  description?: string
  isSystemRole: boolean
  isActive: boolean
  permissions: Permission[]
}

export interface AccessContext {
  userId: string
  sessionId: string
  resource: string
  action: string
  resourceId?: string
  scope?: 'own' | 'team' | 'department' | 'all'
  ipAddress?: string
  userAgent?: string
}

export interface AccessPolicy {
  permission: string
  conditions?: {
    timeRestriction?: { start: string; end: string }
    ipWhitelist?: string[]
    deviceRestriction?: boolean
    mfaRequired?: boolean
  }
}

/**
 * Enhanced RBAC Service for PeopleNest
 * Implements comprehensive role-based access control with:
 * - 5-tier permission system
 * - Resource-level access control
 * - Dynamic permission checking
 * - Audit logging
 * - Conditional access policies
 */
export class RBACService {
  private db: DatabaseService
  private permissionCache = new Map<string, Permission[]>()
  private roleCache = new Map<string, Role>()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutes

  constructor(database: DatabaseService) {
    this.db = database
  }

  /**
   * Check if user has access to a resource with specific action
   */
  async checkAccess(context: AccessContext): Promise<boolean> {
    try {
      // Get user's permissions
      const userPermissions = await this.getUserPermissions(context.userId)
      
      // Check for direct permission match
      const directPermission = this.findMatchingPermission(
        userPermissions, 
        context.resource, 
        context.action, 
        context.scope
      )

      if (directPermission) {
        // Check additional conditions
        const hasConditionalAccess = await this.checkConditionalAccess(
          context, 
          directPermission
        )

        if (hasConditionalAccess) {
          await this.logAccessAttempt(context, true, 'direct_permission')
          return true
        }
      }

      // Check for hierarchical permissions (e.g., admin can do everything)
      const hasHierarchicalAccess = await this.checkHierarchicalAccess(
        context, 
        userPermissions
      )

      if (hasHierarchicalAccess) {
        await this.logAccessAttempt(context, true, 'hierarchical_permission')
        return true
      }

      // Check for resource ownership (e.g., employee accessing own data)
      const hasOwnershipAccess = await this.checkOwnershipAccess(context)

      if (hasOwnershipAccess) {
        await this.logAccessAttempt(context, true, 'ownership_access')
        return true
      }

      await this.logAccessAttempt(context, false, 'insufficient_permissions')
      return false

    } catch (error) {
      logger.error('RBAC access check failed:', error)
      await this.logAccessAttempt(context, false, 'system_error')
      return false
    }
  }

  /**
   * Get user's effective permissions (including role inheritance)
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    const cacheKey = `user_permissions_${userId}`
    
    if (this.permissionCache.has(cacheKey)) {
      const cached = this.permissionCache.get(cacheKey)!
      return cached
    }

    try {
      const query = `
        SELECT DISTINCT p.id, p.name, p.resource, p.action, p.description,
               p.requires_mfa, p.is_sensitive
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN users u ON u.role = rp.role
        WHERE u.id = $1 AND u.is_active = true
        ORDER BY p.resource, p.action
      `

      const result = await this.db.query(query, [userId])
      const permissions: Permission[] = result.rows.map(row => ({
        id: row.id,
        name: row.name,
        resource: row.resource,
        action: row.action,
        description: row.description,
        requiresMfa: row.requires_mfa,
        isSensitive: row.is_sensitive
      }))

      // Cache for 5 minutes
      this.permissionCache.set(cacheKey, permissions)
      setTimeout(() => this.permissionCache.delete(cacheKey), this.cacheExpiry)

      return permissions

    } catch (error) {
      logger.error('Failed to get user permissions:', error)
      return []
    }
  }

  /**
   * Get user's role information
   */
  async getUserRole(userId: string): Promise<Role | null> {
    try {
      const query = `
        SELECT u.role, u.id as user_id
        FROM users u
        WHERE u.id = $1 AND u.is_active = true
      `

      const result = await this.db.query(query, [userId])
      if (result.rows.length === 0) {
        return null
      }

      const roleName = result.rows[0].role
      return await this.getRoleByName(roleName)

    } catch (error) {
      logger.error('Failed to get user role:', error)
      return null
    }
  }

  /**
   * Get role definition by name
   */
  async getRoleByName(roleName: string): Promise<Role | null> {
    if (this.roleCache.has(roleName)) {
      return this.roleCache.get(roleName)!
    }

    try {
      // Get role permissions
      const permissions = await this.getRolePermissions(roleName)
      
      const role: Role = {
        id: roleName,
        name: roleName,
        displayName: this.getRoleDisplayName(roleName),
        description: this.getRoleDescription(roleName),
        isSystemRole: true,
        isActive: true,
        permissions
      }

      // Cache for 5 minutes
      this.roleCache.set(roleName, role)
      setTimeout(() => this.roleCache.delete(roleName), this.cacheExpiry)

      return role

    } catch (error) {
      logger.error('Failed to get role by name:', error)
      return null
    }
  }

  /**
   * Get permissions for a specific role
   */
  private async getRolePermissions(roleName: string): Promise<Permission[]> {
    try {
      const query = `
        SELECT DISTINCT p.id, p.name, p.resource, p.action, p.description,
               p.requires_mfa, p.is_sensitive
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role = $1
        ORDER BY p.resource, p.action
      `

      const result = await this.db.query(query, [roleName])
      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        resource: row.resource,
        action: row.action,
        description: row.description,
        requiresMfa: row.requires_mfa,
        isSensitive: row.is_sensitive
      }))

    } catch (error) {
      logger.error('Failed to get role permissions:', error)
      return []
    }
  }

  /**
   * Find matching permission for resource and action
   */
  private findMatchingPermission(
    permissions: Permission[], 
    resource: string, 
    action: string, 
    scope?: string
  ): Permission | null {
    // Look for exact match first
    const exactMatch = permissions.find(p => 
      p.resource === resource && 
      p.action === action &&
      (scope ? p.scope === scope : true)
    )

    if (exactMatch) return exactMatch

    // Look for wildcard permissions
    const wildcardMatch = permissions.find(p => 
      (p.resource === '*' || p.resource === resource) &&
      (p.action === '*' || p.action === action)
    )

    return wildcardMatch || null
  }

  /**
   * Check hierarchical access (admin roles can access lower-level resources)
   */
  private async checkHierarchicalAccess(
    context: AccessContext, 
    permissions: Permission[]
  ): Promise<boolean> {
    // Super admin has access to everything
    const hasSuperAdminPermission = permissions.some(p => 
      p.name === 'system.admin' || p.resource === '*'
    )

    if (hasSuperAdminPermission) {
      return true
    }

    // HR Admin can access most HR resources
    const hasHRAdminPermission = permissions.some(p => 
      p.name === 'hr_admin' || p.resource === 'hr_admin'
    )

    if (hasHRAdminPermission && this.isHRResource(context.resource)) {
      return true
    }

    return false
  }

  /**
   * Check if user owns the resource being accessed
   */
  private async checkOwnershipAccess(context: AccessContext): Promise<boolean> {
    if (!context.resourceId) return false

    try {
      // Check if user is accessing their own employee record
      if (context.resource === 'employees') {
        const query = `
          SELECT 1 FROM users u 
          JOIN employees e ON u.employee_id = e.id 
          WHERE u.id = $1 AND e.id = $2
        `
        const result = await this.db.query(query, [context.userId, context.resourceId])
        return result.rows.length > 0
      }

      // Add more ownership checks for other resources as needed
      return false

    } catch (error) {
      logger.error('Failed to check ownership access:', error)
      return false
    }
  }

  /**
   * Check conditional access policies
   */
  private async checkConditionalAccess(
    context: AccessContext, 
    permission: Permission
  ): Promise<boolean> {
    // Check if MFA is required for sensitive operations
    if (permission.requiresMfa || permission.isSensitive) {
      const mfaCompleted = await this.checkMFAStatus(context.sessionId)
      if (!mfaCompleted) {
        return false
      }
    }

    // Add more conditional checks as needed (time restrictions, IP whitelist, etc.)
    return true
  }

  /**
   * Check MFA status for session
   */
  private async checkMFAStatus(sessionId: string): Promise<boolean> {
    try {
      const query = `
        SELECT mfa_completed FROM user_sessions 
        WHERE session_id = $1 AND is_active = true
      `
      const result = await this.db.query(query, [sessionId])
      return result.rows.length > 0 && result.rows[0].mfa_completed
    } catch (error) {
      logger.error('Failed to check MFA status:', error)
      return false
    }
  }

  /**
   * Log access attempt for audit purposes
   */
  private async logAccessAttempt(
    context: AccessContext, 
    granted: boolean, 
    reason: string
  ): Promise<void> {
    try {
      await auditLogger.logDataAccess({
        userId: context.userId,
        sessionId: context.sessionId,
        resource: context.resource,
        action: context.action,
        resourceId: context.resourceId,
        accessGranted: granted,
        reason,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        timestamp: new Date()
      })
    } catch (error) {
      logger.error('Failed to log access attempt:', error)
    }
  }

  /**
   * Helper methods
   */
  private isHRResource(resource: string): boolean {
    const hrResources = ['employees', 'payroll', 'benefits', 'performance', 'leave', 'time']
    return hrResources.includes(resource)
  }

  private getRoleDisplayName(roleName: string): string {
    const displayNames: Record<string, string> = {
      'super_admin': 'Super Administrator',
      'hr_admin': 'HR Administrator', 
      'hr_manager': 'HR Manager',
      'manager': 'Manager',
      'employee': 'Employee'
    }
    return displayNames[roleName] || roleName
  }

  private getRoleDescription(roleName: string): string {
    const descriptions: Record<string, string> = {
      'super_admin': 'Full system access with all administrative privileges',
      'hr_admin': 'Complete HR management with administrative capabilities',
      'hr_manager': 'HR operations management with team oversight',
      'manager': 'Team management with employee oversight',
      'employee': 'Standard employee access to personal data and basic functions'
    }
    return descriptions[roleName] || ''
  }

  /**
   * Clear caches (useful for testing or when permissions change)
   */
  clearCache(): void {
    this.permissionCache.clear()
    this.roleCache.clear()
  }
}

// Export singleton instance
const dbService = new DatabaseService()
export const rbacService = new RBACService(dbService)
