import { DatabaseService } from './databaseService'
import { JWTService } from './jwtService'
import { AuditService } from './auditService'
import { logger } from '../utils/logger'
import crypto from 'crypto'

export interface TokenPair {
  accessToken: string
  refreshToken: string
}

export interface SessionData {
  id: string
  userId: string
  deviceFingerprint?: string
  ipAddress?: string
  userAgent?: string
  isActive: boolean
  expiresAt: Date
  createdAt: Date
  updatedAt: Date
}

export interface TokenPayload {
  userId: string
  email?: string
  role?: string
  sessionId?: string
  type?: string
  permissions?: string[]
}

/**
 * Authentication Service
 * Handles token generation, validation, session management, and authentication flows
 */
export class AuthService {
  private db: DatabaseService
  private jwtService: JWTService
  private auditService: AuditService

  constructor() {
    this.db = new DatabaseService()
    this.jwtService = new JWTService()
    this.auditService = new AuditService()
  }

  /**
   * Generate access and refresh token pair
   */
  async generateTokens(
    userId: string, 
    role: string, 
    permissions?: string[], 
    sessionId?: string,
    deviceFingerprint?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<TokenPair> {
    try {
      // Get user details for token
      const userResult = await this.db.query(
        'SELECT id, email, role FROM users WHERE id = $1 AND is_active = true',
        [userId]
      )

      if (userResult.rows.length === 0) {
        throw new Error('User not found or inactive')
      }

      const user = userResult.rows[0]
      const generatedSessionId = sessionId || crypto.randomUUID()

      // Generate token pair using JWT service
      const tokenPair = await this.jwtService.generateTokenPair(
        user.id,
        user.email,
        user.role,
        permissions || [],
        generatedSessionId,
        deviceFingerprint,
        ipAddress,
        userAgent
      )

      // Log token generation
      await this.auditService.logAuditEvent({
        userId: user.id,
        sessionId: generatedSessionId,
        eventType: 'token_generated',
        eventCategory: 'authentication',
        resource: 'auth',
        action: 'generate_tokens',
        details: { tokenType: 'access_refresh_pair' },
        ipAddress,
        userAgent,
        timestamp: new Date(),
        severity: 'low',
        success: true
      })

      return tokenPair
    } catch (error) {
      logger.error('Failed to generate tokens', { userId, error: error.message })
      throw error
    }
  }

  /**
   * Verify and decode token
   */
  async verifyToken(token: string, tokenType: 'access' | 'refresh' | 'mfa_temp' = 'access'): Promise<TokenPayload> {
    try {
      if (tokenType === 'access') {
        const payload = await this.jwtService.verifyAccessToken(token)
        return payload
      } else {
        // For refresh and MFA temp tokens, we'll use a simplified verification
        // In a real implementation, you'd have separate methods for each token type
        const payload = await this.jwtService.verifyAccessToken(token)
        return payload
      }
    } catch (error) {
      logger.warn('Token verification failed', { error: error.message })
      throw error
    }
  }

  /**
   * Create a new session
   */
  async createSession(
    userId: string,
    deviceFingerprint?: string,
    ipAddress?: string,
    userAgent?: string,
    expiresIn: number = 24 * 60 * 60 * 1000 // 24 hours default
  ): Promise<SessionData> {
    try {
      const sessionId = crypto.randomUUID()
      const expiresAt = new Date(Date.now() + expiresIn)

      const result = await this.db.query(`
        INSERT INTO user_sessions (
          id, user_id, device_fingerprint, ip_address, user_agent,
          is_active, expires_at, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING *
      `, [sessionId, userId, deviceFingerprint, ipAddress, userAgent, true, expiresAt])

      const session = result.rows[0]

      // Log session creation
      await this.auditService.logAuditEvent({
        userId,
        sessionId,
        eventType: 'session_created',
        eventCategory: 'authentication',
        resource: 'session',
        action: 'create',
        details: { deviceFingerprint, expiresAt },
        ipAddress,
        userAgent,
        timestamp: new Date(),
        severity: 'low',
        success: true
      })

      return {
        id: session.id,
        userId: session.user_id,
        deviceFingerprint: session.device_fingerprint,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        isActive: session.is_active,
        expiresAt: session.expires_at,
        createdAt: session.created_at,
        updatedAt: session.updated_at
      }
    } catch (error) {
      logger.error('Failed to create session', { userId, error: error.message })
      throw error
    }
  }

  /**
   * Validate existing session
   */
  async validateSession(sessionId: string): Promise<SessionData | null> {
    try {
      const result = await this.db.query(`
        SELECT * FROM user_sessions 
        WHERE id = $1 AND is_active = true AND expires_at > NOW()
      `, [sessionId])

      if (result.rows.length === 0) {
        return null
      }

      const session = result.rows[0]
      
      // Update last accessed time
      await this.db.query(
        'UPDATE user_sessions SET updated_at = NOW() WHERE id = $1',
        [sessionId]
      )

      return {
        id: session.id,
        userId: session.user_id,
        deviceFingerprint: session.device_fingerprint,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        isActive: session.is_active,
        expiresAt: session.expires_at,
        createdAt: session.created_at,
        updatedAt: session.updated_at
      }
    } catch (error) {
      logger.error('Failed to validate session', { sessionId, error: error.message })
      return null
    }
  }

  /**
   * Invalidate session
   */
  async invalidateSession(sessionId: string, userId?: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        'UPDATE user_sessions SET is_active = false, updated_at = NOW() WHERE id = $1',
        [sessionId]
      )

      if (result.rowCount > 0 && userId) {
        // Log session invalidation
        await this.auditService.logAuditEvent({
          userId,
          sessionId,
          eventType: 'session_invalidated',
          eventCategory: 'authentication',
          resource: 'session',
          action: 'invalidate',
          details: { reason: 'manual_logout' },
          timestamp: new Date(),
          severity: 'low',
          success: true
        })
      }

      return result.rowCount > 0
    } catch (error) {
      logger.error('Failed to invalidate session', { sessionId, error: error.message })
      return false
    }
  }

  /**
   * Blacklist token (for logout)
   */
  async blacklistToken(token: string, userId?: string): Promise<boolean> {
    try {
      // Extract token ID from JWT
      const payload = await this.jwtService.verifyAccessToken(token)
      
      // Add to blacklist
      await this.db.query(`
        INSERT INTO token_blacklist (token_id, user_id, blacklisted_at, expires_at)
        VALUES ($1, $2, NOW(), $3)
        ON CONFLICT (token_id) DO NOTHING
      `, [payload.jti || token.substring(0, 50), userId, new Date(payload.exp * 1000)])

      if (userId) {
        // Log token blacklisting
        await this.auditService.logAuditEvent({
          userId,
          sessionId: payload.sessionId,
          eventType: 'token_blacklisted',
          eventCategory: 'authentication',
          resource: 'auth',
          action: 'blacklist_token',
          details: { reason: 'logout' },
          timestamp: new Date(),
          severity: 'low',
          success: true
        })
      }

      return true
    } catch (error) {
      logger.error('Failed to blacklist token', { error: error.message })
      return false
    }
  }

  /**
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const payload = await this.jwtService.verifyAccessToken(token)
      
      const result = await this.db.query(
        'SELECT 1 FROM token_blacklist WHERE token_id = $1 AND expires_at > NOW()',
        [payload.jti || token.substring(0, 50)]
      )

      return result.rows.length > 0
    } catch (error) {
      // If token is invalid, consider it blacklisted
      return true
    }
  }

  /**
   * Cleanup expired sessions and blacklisted tokens
   */
  async cleanup(): Promise<void> {
    try {
      // Remove expired sessions
      const expiredSessions = await this.db.query(
        'DELETE FROM user_sessions WHERE expires_at < NOW() RETURNING count(*)'
      )

      // Remove expired blacklisted tokens
      const expiredTokens = await this.db.query(
        'DELETE FROM token_blacklist WHERE expires_at < NOW() RETURNING count(*)'
      )

      logger.info('Auth cleanup completed', {
        expiredSessions: expiredSessions.rowCount,
        expiredTokens: expiredTokens.rowCount
      })
    } catch (error) {
      logger.error('Auth cleanup failed', { error: error.message })
    }
  }

  /**
   * Get user sessions
   */
  async getUserSessions(userId: string): Promise<SessionData[]> {
    try {
      const result = await this.db.query(`
        SELECT * FROM user_sessions 
        WHERE user_id = $1 AND is_active = true AND expires_at > NOW()
        ORDER BY updated_at DESC
      `, [userId])

      return result.rows.map(session => ({
        id: session.id,
        userId: session.user_id,
        deviceFingerprint: session.device_fingerprint,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        isActive: session.is_active,
        expiresAt: session.expires_at,
        createdAt: session.created_at,
        updatedAt: session.updated_at
      }))
    } catch (error) {
      logger.error('Failed to get user sessions', { userId, error: error.message })
      return []
    }
  }
}
