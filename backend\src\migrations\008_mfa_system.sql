-- Multi-Factor Authentication (MFA) System Migration
-- This migration adds comprehensive MFA support including:
-- 1. TOTP (Time-based One-Time Password) authentication
-- 2. Backup codes for account recovery
-- 3. MFA setup and verification tracking
-- 4. Session-based MFA completion tracking
-- 5. Role-based MFA requirements

-- Create MFA setup table for temporary storage during setup process
CREATE TABLE IF NOT EXISTS user_mfa_setup (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    secret_encrypted BYTEA NOT NULL,
    backup_codes_encrypted BYTEA NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    CONSTRAINT valid_setup_expiry CHECK (expires_at > created_at)
);

-- Create index for cleanup of expired setups
CREATE INDEX IF NOT EXISTS idx_user_mfa_setup_expires ON user_mfa_setup(expires_at);

-- <PERSON>reate main MFA table for active MFA configurations
CREATE TABLE IF NOT EXISTS user_mfa (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    secret_encrypted BYTEA NOT NULL,
    backup_codes_encrypted BYTEA NOT NULL,
    
    -- MFA status
    enabled BOOLEAN DEFAULT false,
    verified BOOLEAN DEFAULT false,
    
    -- Timestamps
    setup_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE,
    disabled_at TIMESTAMP WITH TIME ZONE,
    disabled_by UUID REFERENCES users(id),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_mfa_state CHECK (
        (enabled = false) OR 
        (enabled = true AND verified = true)
    )
);

-- Create indexes for MFA table
CREATE INDEX IF NOT EXISTS idx_user_mfa_enabled ON user_mfa(enabled);
CREATE INDEX IF NOT EXISTS idx_user_mfa_last_used ON user_mfa(last_used);
CREATE INDEX IF NOT EXISTS idx_user_mfa_setup_date ON user_mfa(setup_date);

-- Create MFA verification attempts log for security monitoring
CREATE TABLE IF NOT EXISTS mfa_verification_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    
    -- Attempt details
    verification_type VARCHAR(20) NOT NULL, -- 'totp', 'backup_code'
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(100),
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_verification_type CHECK (verification_type IN ('totp', 'backup_code'))
);

-- Create indexes for verification attempts
CREATE INDEX IF NOT EXISTS idx_mfa_attempts_user_id ON mfa_verification_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_attempts_attempted_at ON mfa_verification_attempts(attempted_at);
CREATE INDEX IF NOT EXISTS idx_mfa_attempts_success ON mfa_verification_attempts(success);
CREATE INDEX IF NOT EXISTS idx_mfa_attempts_ip ON mfa_verification_attempts(ip_address);

-- Create MFA policy table for role-based MFA requirements
CREATE TABLE IF NOT EXISTS mfa_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Policy rules
    required_for_roles TEXT[] DEFAULT '{}',
    required_for_permissions TEXT[] DEFAULT '{}',
    grace_period_hours INTEGER DEFAULT 0,
    max_sessions_without_mfa INTEGER DEFAULT 0,
    
    -- Policy status
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Insert default MFA policies
INSERT INTO mfa_policies (name, description, required_for_roles, required_for_permissions, is_active) VALUES
('Admin MFA Policy', 'Require MFA for all administrative roles', 
 ARRAY['super_admin', 'hr_admin'], 
 ARRAY['system:admin:all', 'users:manage:all'], 
 true),
('Sensitive Data Policy', 'Require MFA for accessing sensitive data', 
 ARRAY[]::TEXT[], 
 ARRAY['payroll:read:all', 'payroll:process:all', 'employees:delete:all'], 
 true),
('HR Manager Policy', 'Require MFA for HR management functions', 
 ARRAY['hr_manager'], 
 ARRAY['employees:create:all', 'employees:update:all'], 
 true)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for MFA policies
CREATE INDEX IF NOT EXISTS idx_mfa_policies_active ON mfa_policies(is_active);
CREATE INDEX IF NOT EXISTS idx_mfa_policies_roles ON mfa_policies USING GIN(required_for_roles);
CREATE INDEX IF NOT EXISTS idx_mfa_policies_permissions ON mfa_policies USING GIN(required_for_permissions);

-- Add MFA completion tracking to user_sessions (if not already added)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_sessions' AND column_name = 'mfa_completed') THEN
        ALTER TABLE user_sessions 
        ADD COLUMN mfa_completed BOOLEAN DEFAULT false,
        ADD COLUMN mfa_completed_at TIMESTAMP WITH TIME ZONE,
        ADD COLUMN mfa_required BOOLEAN DEFAULT false;
    END IF;
END $$;

-- Create index for MFA session tracking (after columns are added)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'user_sessions' AND column_name = 'mfa_required') THEN
        CREATE INDEX IF NOT EXISTS idx_user_sessions_mfa ON user_sessions(mfa_completed, mfa_required);
    END IF;
END $$;

-- Create function to check if MFA is required for user
CREATE OR REPLACE FUNCTION is_mfa_required_for_user(user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions TEXT[];
    policy_record RECORD;
BEGIN
    -- Get user role
    SELECT role INTO user_role FROM users WHERE id = user_id_param;
    
    -- Get user permissions
    SELECT ARRAY_AGG(p.name) INTO user_permissions
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    WHERE rp.role = user_role AND rp.is_active = true;
    
    -- Check active MFA policies
    FOR policy_record IN 
        SELECT required_for_roles, required_for_permissions 
        FROM mfa_policies 
        WHERE is_active = true
    LOOP
        -- Check if user role requires MFA
        IF user_role = ANY(policy_record.required_for_roles) THEN
            RETURN true;
        END IF;
        
        -- Check if user has any permission that requires MFA
        IF user_permissions && policy_record.required_for_permissions THEN
            RETURN true;
        END IF;
    END LOOP;
    
    RETURN false;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up expired MFA setups
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_setups()
RETURNS void AS $$
BEGIN
    DELETE FROM user_mfa_setup WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create function to update MFA session requirements
CREATE OR REPLACE FUNCTION update_session_mfa_requirements()
RETURNS TRIGGER AS $$
BEGIN
    -- Update session MFA requirement when user logs in
    IF TG_OP = 'INSERT' THEN
        UPDATE user_sessions 
        SET mfa_required = is_mfa_required_for_user(NEW.user_id)
        WHERE session_id = NEW.session_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set MFA requirements for new sessions
DROP TRIGGER IF EXISTS trigger_update_session_mfa_requirements ON user_sessions;
CREATE TRIGGER trigger_update_session_mfa_requirements
    AFTER INSERT ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_session_mfa_requirements();

-- Create function to log MFA verification attempts
CREATE OR REPLACE FUNCTION log_mfa_verification_attempt(
    user_id_param UUID,
    session_id_param VARCHAR(255),
    verification_type_param VARCHAR(20),
    success_param BOOLEAN,
    failure_reason_param VARCHAR(100) DEFAULT NULL,
    ip_address_param INET DEFAULT NULL,
    user_agent_param TEXT DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    INSERT INTO mfa_verification_attempts (
        user_id, session_id, verification_type, success, 
        failure_reason, ip_address, user_agent
    ) VALUES (
        user_id_param, session_id_param, verification_type_param, 
        success_param, failure_reason_param, ip_address_param, user_agent_param
    );
END;
$$ LANGUAGE plpgsql;

-- Create view for MFA status overview
CREATE OR REPLACE VIEW mfa_status_overview AS
SELECT 
    u.id as user_id,
    u.employee_id,
    u.email,
    u.role,
    COALESCE(mfa.enabled, false) as mfa_enabled,
    COALESCE(mfa.verified, false) as mfa_verified,
    mfa.setup_date,
    mfa.last_used,
    is_mfa_required_for_user(u.id) as mfa_required,
    CASE 
        WHEN is_mfa_required_for_user(u.id) AND NOT COALESCE(mfa.enabled, false) THEN 'required_not_setup'
        WHEN is_mfa_required_for_user(u.id) AND COALESCE(mfa.enabled, false) THEN 'required_enabled'
        WHEN NOT is_mfa_required_for_user(u.id) AND COALESCE(mfa.enabled, false) THEN 'optional_enabled'
        ELSE 'optional_disabled'
    END as mfa_status
FROM users u
LEFT JOIN user_mfa mfa ON u.id = mfa.user_id
WHERE u.is_active = true;

-- Create view for MFA security metrics
CREATE OR REPLACE VIEW mfa_security_metrics AS
SELECT 
    DATE_TRUNC('day', attempted_at) as date,
    verification_type,
    COUNT(*) as total_attempts,
    COUNT(*) FILTER (WHERE success = true) as successful_attempts,
    COUNT(*) FILTER (WHERE success = false) as failed_attempts,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT ip_address) as unique_ips
FROM mfa_verification_attempts
WHERE attempted_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', attempted_at), verification_type
ORDER BY date DESC, verification_type;

-- Add comments for documentation
COMMENT ON TABLE user_mfa_setup IS 'Temporary storage for MFA setup process with expiration';
COMMENT ON TABLE user_mfa IS 'Active MFA configurations for users';
COMMENT ON TABLE mfa_verification_attempts IS 'Log of all MFA verification attempts for security monitoring';
COMMENT ON TABLE mfa_policies IS 'Role and permission-based MFA requirement policies';
COMMENT ON FUNCTION is_mfa_required_for_user(UUID) IS 'Check if MFA is required for a specific user based on policies';
COMMENT ON VIEW mfa_status_overview IS 'Comprehensive view of MFA status for all users';
COMMENT ON VIEW mfa_security_metrics IS 'Daily MFA usage and security metrics';

-- Create scheduled job to clean up expired setups (if pg_cron is available)
-- This would typically be handled by application logic or external cron job
-- SELECT cron.schedule('cleanup-mfa-setups', '0 */6 * * *', 'SELECT cleanup_expired_mfa_setups();');
