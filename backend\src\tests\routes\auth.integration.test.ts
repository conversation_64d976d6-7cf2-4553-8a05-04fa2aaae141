import request from 'supertest'
import express from 'express'
import authRoutes from '../../routes/auth'
import { DatabaseService } from '../../services/databaseService'
import { AuthService } from '../../services/authService'
import { MFAService } from '../../services/mfaService'
import bcrypt from 'bcrypt'

// Mock dependencies
jest.mock('../../services/databaseService')
jest.mock('../../services/authService')
jest.mock('../../services/mfaService')
jest.mock('../../utils/logger')
jest.mock('bcrypt')

const mockDb = {
  query: jest.fn()
}

const mockAuthService = {
  generateTokens: jest.fn(),
  verifyToken: jest.fn(),
  blacklistToken: jest.fn(),
  createSession: jest.fn(),
  validateSession: jest.fn(),
  invalidateSession: jest.fn()
}

const mockMFAService = {
  isMFARequired: jest.fn(),
  verifyMFAToken: jest.fn()
}

// Mock constructors
;(DatabaseService as jest.MockedClass<typeof DatabaseService>).mockImplementation(() => mockDb as any)
;(AuthService as jest.MockedClass<typeof AuthService>).mockImplementation(() => mockAuthService as any)
;(MFAService as jest.MockedClass<typeof MFAService>).mockImplementation(() => mockMFAService as any)

// Setup Express app for testing
const app = express()
app.use(express.json())
app.use('/auth', authRoutes)

describe('Auth Routes Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /auth/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'password123',
      deviceFingerprint: 'device-123'
    }

    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password_hash: 'hashed-password',
        role: 'employee',
        is_active: true,
        failed_login_attempts: 0,
        locked_until: null
      }

      const mockTokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token'
      }

      const mockSession = {
        id: 'session-123',
        userId: 'user-123'
      }

      // Mock database user lookup
      mockDb.query.mockResolvedValueOnce({ rows: [mockUser] })
      
      // Mock password verification
      ;(bcrypt.compare as jest.Mock).mockResolvedValueOnce(true)
      
      // Mock MFA requirement check
      mockMFAService.isMFARequired.mockResolvedValueOnce(false)
      
      // Mock token generation
      mockAuthService.generateTokens.mockResolvedValueOnce(mockTokens)
      
      // Mock session creation
      mockAuthService.createSession.mockResolvedValueOnce(mockSession)
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: mockUser.id,
            email: mockUser.email,
            role: mockUser.role
          },
          tokens: mockTokens,
          session: mockSession
        }
      })

      expect(bcrypt.compare).toHaveBeenCalledWith(validLoginData.password, mockUser.password_hash)
      expect(mockAuthService.generateTokens).toHaveBeenCalledWith(mockUser.id, mockUser.role)
    })

    it('should require MFA when enabled for user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password_hash: 'hashed-password',
        role: 'admin',
        is_active: true,
        failed_login_attempts: 0,
        locked_until: null
      }

      // Mock database user lookup
      mockDb.query.mockResolvedValueOnce({ rows: [mockUser] })
      
      // Mock password verification
      ;(bcrypt.compare as jest.Mock).mockResolvedValueOnce(true)
      
      // Mock MFA requirement check
      mockMFAService.isMFARequired.mockResolvedValueOnce(true)
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: 'MFA verification required',
        data: {
          mfaRequired: true,
          tempToken: expect.any(String)
        }
      })
    })

    it('should reject invalid credentials', async () => {
      // Mock user not found
      mockDb.query.mockResolvedValueOnce({ rows: [] })
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData)
        .expect(401)

      expect(response.body).toMatchObject({
        error: 'Authentication Failed',
        message: 'Invalid email or password'
      })
    })

    it('should reject login for locked account', async () => {
      const lockedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password_hash: 'hashed-password',
        role: 'employee',
        is_active: true,
        failed_login_attempts: 5,
        locked_until: new Date(Date.now() + 3600000) // 1 hour from now
      }

      // Mock database user lookup
      mockDb.query.mockResolvedValueOnce({ rows: [lockedUser] })
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData)
        .expect(423)

      expect(response.body).toMatchObject({
        error: 'Account Locked',
        message: expect.stringContaining('Account is temporarily locked')
      })
    })

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: 'invalid-email',
          password: '123' // too short
        })
        .expect(400)

      expect(response.body).toMatchObject({
        error: 'Validation Error',
        details: expect.arrayContaining([
          expect.objectContaining({
            msg: expect.stringContaining('email')
          }),
          expect.objectContaining({
            msg: expect.stringContaining('password')
          })
        ])
      })
    })
  })

  describe('POST /auth/mfa/verify', () => {
    const validMFAData = {
      tempToken: 'temp-token-123',
      mfaToken: '123456',
      deviceFingerprint: 'device-123'
    }

    it('should complete login after successful MFA verification', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'admin'
      }

      const mockTokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token'
      }

      const mockSession = {
        id: 'session-123',
        userId: 'user-123'
      }

      // Mock temp token verification
      mockAuthService.verifyToken.mockResolvedValueOnce({
        userId: mockUser.id,
        type: 'mfa_temp'
      })
      
      // Mock user lookup
      mockDb.query.mockResolvedValueOnce({ rows: [mockUser] })
      
      // Mock MFA verification
      mockMFAService.verifyMFAToken.mockResolvedValueOnce({
        success: true,
        method: 'totp'
      })
      
      // Mock token generation
      mockAuthService.generateTokens.mockResolvedValueOnce(mockTokens)
      
      // Mock session creation
      mockAuthService.createSession.mockResolvedValueOnce(mockSession)
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/mfa/verify')
        .send(validMFAData)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: 'MFA verification successful',
        data: {
          user: mockUser,
          tokens: mockTokens,
          session: mockSession
        }
      })

      expect(mockMFAService.verifyMFAToken).toHaveBeenCalledWith(
        mockUser.id,
        validMFAData.mfaToken,
        undefined
      )
    })

    it('should reject invalid MFA token', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'admin'
      }

      // Mock temp token verification
      mockAuthService.verifyToken.mockResolvedValueOnce({
        userId: mockUser.id,
        type: 'mfa_temp'
      })
      
      // Mock user lookup
      mockDb.query.mockResolvedValueOnce({ rows: [mockUser] })
      
      // Mock MFA verification failure
      mockMFAService.verifyMFAToken.mockResolvedValueOnce({
        success: false,
        method: null
      })
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/mfa/verify')
        .send(validMFAData)
        .expect(401)

      expect(response.body).toMatchObject({
        error: 'MFA Verification Failed',
        message: 'Invalid MFA token'
      })
    })

    it('should reject invalid temp token', async () => {
      // Mock temp token verification failure
      mockAuthService.verifyToken.mockRejectedValueOnce(new Error('Invalid token'))

      const response = await request(app)
        .post('/auth/mfa/verify')
        .send(validMFAData)
        .expect(401)

      expect(response.body).toMatchObject({
        error: 'Authentication Failed',
        message: 'Invalid or expired temporary token'
      })
    })
  })

  describe('POST /auth/refresh', () => {
    it('should refresh tokens successfully', async () => {
      const refreshToken = 'valid-refresh-token'
      const newTokens = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token'
      }

      // Mock token verification
      mockAuthService.verifyToken.mockResolvedValueOnce({
        userId: 'user-123',
        type: 'refresh'
      })
      
      // Mock session validation
      mockAuthService.validateSession.mockResolvedValueOnce({
        id: 'session-123',
        userId: 'user-123',
        isValid: true
      })
      
      // Mock new token generation
      mockAuthService.generateTokens.mockResolvedValueOnce(newTokens)
      
      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: 'Tokens refreshed successfully',
        data: {
          tokens: newTokens
        }
      })
    })

    it('should reject invalid refresh token', async () => {
      const refreshToken = 'invalid-refresh-token'

      // Mock token verification failure
      mockAuthService.verifyToken.mockRejectedValueOnce(new Error('Invalid token'))

      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(401)

      expect(response.body).toMatchObject({
        error: 'Authentication Failed',
        message: 'Invalid or expired refresh token'
      })
    })
  })

  describe('POST /auth/logout', () => {
    it('should logout successfully', async () => {
      const mockUser = { id: 'user-123', role: 'employee' }
      const mockSession = { id: 'session-123' }

      // Mock session invalidation
      mockAuthService.invalidateSession.mockResolvedValueOnce(true)

      // Mock token blacklisting
      mockAuthService.blacklistToken.mockResolvedValueOnce(true)

      // Mock audit logging
      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      const response = await request(app)
        .post('/auth/logout')
        .set('Authorization', 'Bearer valid-token')
        .send()

      // Note: This test would need proper auth middleware setup to work fully
      // For now, we're testing the route structure
      expect(response.status).toBeLessThan(500) // Should not be a server error
    })
  })

  describe('Security Features', () => {
    describe('Rate Limiting', () => {
      it('should implement rate limiting on login attempts', async () => {
        const loginData = {
          email: '<EMAIL>',
          password: 'wrongpassword',
          deviceFingerprint: 'device-123'
        }

        // Mock user not found for multiple attempts
        mockDb.query.mockResolvedValue({ rows: [] })

        // Make multiple rapid requests
        const requests = Array(6).fill(null).map(() =>
          request(app)
            .post('/auth/login')
            .send(loginData)
        )

        const responses = await Promise.all(requests)

        // Should have some rate limiting response (429 or similar)
        // Note: Actual rate limiting would be implemented in middleware
        responses.forEach(response => {
          expect([400, 401, 429]).toContain(response.status)
        })
      })
    })

    describe('Account Lockout', () => {
      it('should lock account after multiple failed attempts', async () => {
        const mockUser = {
          id: 'user-123',
          email: '<EMAIL>',
          password_hash: 'hashed-password',
          role: 'employee',
          is_active: true,
          failed_login_attempts: 4, // One more will lock
          locked_until: null
        }

        const loginData = {
          email: '<EMAIL>',
          password: 'wrongpassword',
          deviceFingerprint: 'device-123'
        }

        // Mock database user lookup
        mockDb.query.mockResolvedValueOnce({ rows: [mockUser] })

        // Mock password verification failure
        ;(bcrypt.compare as jest.Mock).mockResolvedValueOnce(false)

        // Mock failed attempt update
        mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })

        // Mock audit logging
        mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

        const response = await request(app)
          .post('/auth/login')
          .send(loginData)
          .expect(401)

        expect(response.body.message).toContain('Invalid email or password')

        // Verify that failed attempts are incremented
        expect(mockDb.query).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE users SET failed_login_attempts'),
          expect.any(Array)
        )
      })
    })

    describe('Input Validation', () => {
      it('should sanitize and validate all inputs', async () => {
        const maliciousData = {
          email: '<script>alert("xss")</script>@example.com',
          password: 'password123',
          deviceFingerprint: '"><script>alert("xss")</script>'
        }

        const response = await request(app)
          .post('/auth/login')
          .send(maliciousData)
          .expect(400)

        expect(response.body).toMatchObject({
          error: 'Validation Error',
          details: expect.arrayContaining([
            expect.objectContaining({
              msg: expect.stringContaining('email')
            })
          ])
        })
      })

      it('should reject SQL injection attempts', async () => {
        const sqlInjectionData = {
          email: "<EMAIL>'; DROP TABLE users; --",
          password: 'password123',
          deviceFingerprint: 'device-123'
        }

        const response = await request(app)
          .post('/auth/login')
          .send(sqlInjectionData)
          .expect(400)

        expect(response.body).toMatchObject({
          error: 'Validation Error'
        })
      })
    })

    describe('Session Security', () => {
      it('should generate secure session tokens', async () => {
        const mockUser = {
          id: 'user-123',
          email: '<EMAIL>',
          password_hash: 'hashed-password',
          role: 'employee',
          is_active: true,
          failed_login_attempts: 0,
          locked_until: null
        }

        const mockTokens = {
          accessToken: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
          refreshToken: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...'
        }

        const mockSession = {
          id: 'session-123',
          userId: 'user-123'
        }

        // Mock successful login flow
        mockDb.query.mockResolvedValueOnce({ rows: [mockUser] })
        ;(bcrypt.compare as jest.Mock).mockResolvedValueOnce(true)
        mockMFAService.isMFARequired.mockResolvedValueOnce(false)
        mockAuthService.generateTokens.mockResolvedValueOnce(mockTokens)
        mockAuthService.createSession.mockResolvedValueOnce(mockSession)
        mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

        const response = await request(app)
          .post('/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123',
            deviceFingerprint: 'device-123'
          })
          .expect(200)

        // Verify tokens are properly formatted JWTs
        expect(response.body.data.tokens.accessToken).toMatch(/^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/)
        expect(response.body.data.tokens.refreshToken).toMatch(/^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/)
      })
    })
  })
})
