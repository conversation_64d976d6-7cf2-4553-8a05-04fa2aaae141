import jwt from 'jsonwebtoken'
import { Request, Response, NextFunction } from 'express'
import { DatabaseService } from '../services/databaseService'
import { logger } from '../utils/logger'
import { jwtService, TokenPayload } from '../services/jwtService'
import { auditLogger } from '../utils/auditLogger'

interface AuthenticatedUser {
  id: string
  employeeId: string
  email: string
  firstName: string
  lastName: string
  role: string
  permissions: string[]
  departmentId?: string
  managerId?: string
  sessionId?: string
  deviceFingerprint?: string
}

declare global {
  namespace Express {
    interface Request {
      user: AuthenticatedUser
    }
  }
}

const db = new DatabaseService()

/**
 * Enhanced authentication middleware
 * Verifies JW<PERSON> token using enhanced JWT service and attaches user information to request
 */
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No valid authorization token provided',
        code: 'MISSING_TOKEN'
      })
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify token using enhanced JWT service
    let tokenPayload: TokenPayload
    try {
      tokenPayload = await jwtService.verifyAccessToken(token)
    } catch (error) {
      logger.warn('Token verification failed', {
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path
      })

      // Log failed authentication attempt
      await auditLogger.logAuthEvent(
        'LOGIN_FAILED',
        'unknown',
        'unknown',
        req,
        { reason: 'invalid_token', error: error.message }
      )

      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      })
    }

    // Handle development mode mock token
    if (process.env.NODE_ENV === 'development' && token === 'mock-token') {
      // Create a mock user for development
      req.user = {
        id: 'mock-user-id',
        employeeId: null,
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'super_admin',
        permissions: ['super_admin', 'hr_admin', 'hr', 'manager', 'employee_read_all', 'employee_update_all', 'department_read', 'department_write'],
        departmentId: null,
        managerId: null,
        sessionId: 'mock-session',
        deviceFingerprint: 'mock-device'
      }
      return next()
    }

    // Get user details from database using token payload
    const userResult = await db.query(`
      SELECT
        u.id,
        u.email,
        u.role,
        u.is_active,
        u.last_login,
        e.id as employee_id,
        e.first_name,
        e.last_name,
        e.department_id,
        e.manager_id,
        e.status as employee_status
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.id = $1 AND u.is_active = true
    `, [tokenPayload.userId])

    if (userResult.rows.length === 0) {
      logger.warn('User not found or inactive during authentication', {
        userId: tokenPayload.userId,
        ip: req.ip
      })

      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      })
    }

    const user = userResult.rows[0]

    // Check if employee is active (if user is linked to an employee)
    if (user.employee_id && user.employee_status !== 'active') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Employee account is not active',
        code: 'EMPLOYEE_INACTIVE'
      })
    }

    // Verify device fingerprint if available
    const currentDeviceFingerprint = req.headers['x-device-fingerprint'] as string
    if (tokenPayload.deviceFingerprint && currentDeviceFingerprint) {
      if (tokenPayload.deviceFingerprint !== currentDeviceFingerprint) {
        logger.warn('Device fingerprint mismatch', {
          userId: user.id,
          sessionId: tokenPayload.sessionId,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        })

        // In production, you might want to revoke the session
        // await jwtService.revokeAllTokens(tokenPayload.sessionId)
      }
    }

    // Attach user information to request
    req.user = {
      id: user.id,
      employeeId: user.employee_id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      role: user.role,
      permissions: tokenPayload.permissions,
      departmentId: user.department_id,
      managerId: user.manager_id,
      sessionId: tokenPayload.sessionId,
      deviceFingerprint: tokenPayload.deviceFingerprint
    }

    // Update last activity timestamp
    await db.query(
      'UPDATE users SET last_activity = NOW() WHERE id = $1',
      [user.id]
    )

    next()
  } catch (error) {
    logger.error('Authentication middleware error:', {
      error: error.message,
      stack: error.stack,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    })

    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication error',
      code: 'AUTH_ERROR'
    })
  }
}

/**
 * Optional authentication middleware
 * Attaches user information if token is provided, but doesn't require it
 */
export const optionalAuthMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next() // Continue without authentication
    }

    // Use the main auth middleware logic
    await authMiddleware(req, res, next)
  } catch (error) {
    // If authentication fails, continue without user context
    next()
  }
}

/**
 * Get user permissions based on role
 */
async function getUserPermissions(role: string): Promise<string[]> {
  const rolePermissions: Record<string, string[]> = {
    'super_admin': [
      'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
    ],
    'hr_admin': [
      'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
    ],
    'hr': [
      'hr', 'employee', 'recruiter'
    ],
    'manager': [
      'manager', 'employee'
    ],
    'employee': [
      'employee'
    ],
    'recruiter': [
      'recruiter', 'employee'
    ],
    'payroll': [
      'payroll', 'employee'
    ],
    'it': [
      'it', 'employee'
    ]
  }

  return rolePermissions[role] || ['employee']
}

/**
 * Role-based access control middleware
 */
export const requireRole = (...allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient role permissions'
      })
    }

    next()
  }
}

/**
 * Permission-based access control middleware
 */
export const requirePermission = (...requiredPermissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const hasPermission = requiredPermissions.some(permission => 
      req.user.permissions.includes(permission)
    )

    if (!hasPermission) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      })
    }

    next()
  }
}

/**
 * Department access control middleware
 */
export const requireDepartmentAccess = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required'
    })
  }

  // HR and HR Admin can access all departments
  if (req.user.permissions.includes('hr_admin') || req.user.permissions.includes('hr')) {
    return next()
  }

  // Managers can access their own department
  if (req.user.permissions.includes('manager') && req.user.departmentId) {
    return next()
  }

  // Employees can only access their own data
  if (req.user.permissions.includes('employee')) {
    return next()
  }

  return res.status(403).json({
    error: 'Forbidden',
    message: 'Department access denied'
  })
}

/**
 * Self or manager access middleware
 * Allows access if user is accessing their own data or is a manager/HR
 */
export const requireSelfOrManagerAccess = (employeeIdParam: string = 'employeeId') => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const targetEmployeeId = req.params[employeeIdParam] || req.body[employeeIdParam]

    // HR and HR Admin can access all employee data
    if (req.user.permissions.includes('hr_admin') || req.user.permissions.includes('hr')) {
      return next()
    }

    // Managers can access their team members' data
    if (req.user.permissions.includes('manager')) {
      return next() // Additional logic needed to check if employee reports to this manager
    }

    // Employees can only access their own data
    if (req.user.employeeId === targetEmployeeId) {
      return next()
    }

    return res.status(403).json({
      error: 'Forbidden',
      message: 'Access denied'
    })
  }
}
