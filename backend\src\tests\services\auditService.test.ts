import { AuditService, SecurityMonitoringService, AuditEvent, SecurityEvent, DataAccessEvent } from '../../services/auditService'
import { DatabaseService } from '../../services/databaseService'

// Mock the database service
jest.mock('../../services/databaseService')
jest.mock('../../utils/logger')

const mockDb = {
  query: jest.fn()
}

// Mock DatabaseService constructor
;(DatabaseService as jest.MockedClass<typeof DatabaseService>).mockImplementation(() => mockDb as any)

describe('AuditService', () => {
  let auditService: AuditService
  
  beforeEach(() => {
    auditService = new AuditService()
    jest.clearAllMocks()
  })

  describe('logAuditEvent', () => {
    it('should log audit event successfully', async () => {
      const event: AuditEvent = {
        userId: 'user-123',
        sessionId: 'session-456',
        eventType: 'login_success',
        eventCategory: 'authentication',
        resource: 'auth',
        action: 'login',
        details: { method: 'password' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        timestamp: new Date(),
        severity: 'medium',
        success: true,
        riskScore: 30
      }

      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })

      await auditService.logAuditEvent(event)

      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO audit_events'),
        expect.arrayContaining([
          event.userId,
          event.sessionId,
          event.eventType,
          event.eventCategory,
          event.resource,
          event.resourceId,
          event.action,
          JSON.stringify(event.details),
          event.ipAddress,
          event.userAgent,
          event.timestamp,
          event.severity,
          event.success,
          event.errorMessage,
          event.riskScore,
          event.complianceFlags || []
        ])
      )
    })

    it('should handle database errors gracefully', async () => {
      const event: AuditEvent = {
        eventType: 'test_event',
        eventCategory: 'system_event',
        timestamp: new Date(),
        severity: 'low',
        success: true
      }

      mockDb.query.mockRejectedValueOnce(new Error('Database error'))

      // Should not throw error
      await expect(auditService.logAuditEvent(event)).resolves.toBeUndefined()
    })
  })

  describe('logSecurityEvent', () => {
    it('should log security event to both tables', async () => {
      const event: SecurityEvent = {
        userId: 'user-123',
        eventType: 'suspicious_login',
        details: { reason: 'unusual_location' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        timestamp: new Date(),
        severity: 'high',
        riskScore: 80
      }

      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      await auditService.logSecurityEvent(event)

      // Should call query twice - once for audit_events, once for security_events
      expect(mockDb.query).toHaveBeenCalledTimes(2)
      expect(mockDb.query).toHaveBeenNthCalledWith(1, 
        expect.stringContaining('INSERT INTO audit_events'),
        expect.any(Array)
      )
      expect(mockDb.query).toHaveBeenNthCalledWith(2,
        expect.stringContaining('INSERT INTO security_events'),
        expect.any(Array)
      )
    })
  })

  describe('logDataAccess', () => {
    it('should log data access for GDPR compliance', async () => {
      const event: DataAccessEvent = {
        userId: 'user-123',
        resource: 'employees',
        resourceId: 'emp-456',
        action: 'read',
        dataClassification: 'confidential',
        recordCount: 1,
        fieldAccessed: ['name', 'email'],
        purpose: 'HR Management',
        legalBasis: 'legitimate_interest',
        timestamp: new Date()
      }

      mockDb.query.mockResolvedValue({ rows: [], rowCount: 1 })

      await auditService.logDataAccess(event)

      // Should call query twice - once for data_access_log, once for audit_events
      expect(mockDb.query).toHaveBeenCalledTimes(2)
      expect(mockDb.query).toHaveBeenNthCalledWith(1,
        expect.stringContaining('INSERT INTO data_access_log'),
        expect.arrayContaining([
          event.userId,
          event.sessionId,
          event.resource,
          event.resourceId,
          event.action,
          event.dataClassification,
          event.recordCount,
          event.fieldAccessed,
          event.purpose,
          event.legalBasis,
          event.ipAddress,
          event.userAgent,
          event.timestamp
        ])
      )
    })
  })

  describe('getUserAuditTrail', () => {
    it('should retrieve user audit trail', async () => {
      const userId = 'user-123'
      const mockEvents = [
        {
          event_type: 'login_success',
          event_category: 'authentication',
          timestamp: new Date(),
          severity: 'medium'
        }
      ]

      mockDb.query.mockResolvedValueOnce({ rows: mockEvents })

      const result = await auditService.getUserAuditTrail(userId)

      expect(result).toEqual(mockEvents)
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT event_type, event_category'),
        [userId]
      )
    })

    it('should handle date range filters', async () => {
      const userId = 'user-123'
      const startDate = new Date('2023-01-01')
      const endDate = new Date('2023-12-31')

      mockDb.query.mockResolvedValueOnce({ rows: [] })

      await auditService.getUserAuditTrail(userId, startDate, endDate)

      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('AND timestamp >= $2'),
        [userId, startDate.toISOString(), endDate.toISOString()]
      )
    })
  })

  describe('getSecurityIncidents', () => {
    it('should retrieve security incidents', async () => {
      const mockIncidents = [
        {
          event_type: 'failed_login_attempts',
          severity: 'high',
          risk_score: 85,
          timestamp: new Date()
        }
      ]

      mockDb.query.mockResolvedValueOnce({ rows: mockIncidents })

      const result = await auditService.getSecurityIncidents('high', 50)

      expect(result).toEqual(mockIncidents)
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('WHERE severity = $1'),
        ['high', 50]
      )
    })
  })

  describe('cleanupAuditData', () => {
    it('should cleanup old audit data', async () => {
      const retentionDays = 365
      mockDb.query.mockResolvedValue({ rowCount: 100 })

      await auditService.cleanupAuditData(retentionDays)

      // Should call query 3 times for different tables
      expect(mockDb.query).toHaveBeenCalledTimes(3)
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM audit_events'),
        expect.any(Array)
      )
    })
  })
})

describe('SecurityMonitoringService', () => {
  let securityService: SecurityMonitoringService
  
  beforeEach(() => {
    securityService = new SecurityMonitoringService()
    jest.clearAllMocks()
  })

  describe('analyzeEvent', () => {
    it('should analyze event for suspicious patterns', async () => {
      const event: AuditEvent = {
        eventType: 'login_failed',
        eventCategory: 'authentication',
        ipAddress: '***********',
        timestamp: new Date(),
        severity: 'medium',
        success: false
      }

      // Mock getting active patterns
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          id: 'pattern-1',
          pattern_name: 'Multiple Failed Logins',
          pattern_type: 'frequency',
          pattern_rules: { event_type: 'login_failed', count_threshold: 5 },
          time_window_minutes: 15,
          risk_score: 70,
          alert_level: 'high'
        }]
      })

      // Mock frequency check
      mockDb.query.mockResolvedValueOnce({
        rows: [{ count: '6' }] // Above threshold
      })

      // Mock alert insertion
      mockDb.query.mockResolvedValueOnce({ rows: [], rowCount: 1 })

      await securityService.analyzeEvent(event)

      // Should check patterns and potentially trigger alert
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM suspicious_activity_patterns'),
        []
      )
    })
  })

  describe('checkFrequencyPattern', () => {
    it('should detect frequency-based suspicious activity', async () => {
      const event: AuditEvent = {
        eventType: 'login_failed',
        eventCategory: 'authentication',
        ipAddress: '***********',
        timestamp: new Date(),
        severity: 'medium',
        success: false
      }

      const pattern = {
        pattern_type: 'frequency',
        pattern_rules: { event_type: 'login_failed', count_threshold: 5 },
        time_window_minutes: 15
      }

      // Mock count query returning above threshold
      mockDb.query.mockResolvedValueOnce({
        rows: [{ count: '6' }]
      })

      const result = await securityService['checkFrequencyPattern'](event, pattern, pattern.pattern_rules)

      expect(result).toBe(true)
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('COUNT(*) as count'),
        ['login_failed', '***********']
      )
    })
  })

  describe('checkLocationPattern', () => {
    it('should detect unusual location access', async () => {
      const event: AuditEvent = {
        userId: 'user-123',
        eventType: 'login_success',
        eventCategory: 'authentication',
        ipAddress: '********', // New IP
        timestamp: new Date(),
        severity: 'medium',
        success: true
      }

      const pattern = {
        pattern_type: 'location',
        pattern_rules: { event_type: 'login_success', location_variance: 'high' }
      }

      // Mock query returning no previous IPs (new location)
      mockDb.query.mockResolvedValueOnce({
        rows: []
      })

      const result = await securityService['checkLocationPattern'](event, pattern, pattern.pattern_rules)

      expect(result).toBe(true)
    })
  })

  describe('checkTimePattern', () => {
    it('should detect off-hours access', async () => {
      // Create event with off-hours timestamp
      const offHoursDate = new Date()
      offHoursDate.setHours(22) // 10 PM

      const event: AuditEvent = {
        eventType: 'data_access',
        eventCategory: 'data_access',
        timestamp: offHoursDate,
        severity: 'medium',
        success: true
      }

      const pattern = {
        pattern_type: 'time',
        pattern_rules: { time_range: 'off_hours' }
      }

      const result = await securityService['checkTimePattern'](event, pattern, pattern.pattern_rules)

      expect(result).toBe(true)
    })

    it('should not trigger for business hours access', async () => {
      // Create event with business hours timestamp
      const businessHoursDate = new Date()
      businessHoursDate.setHours(10) // 10 AM

      const event: AuditEvent = {
        eventType: 'data_access',
        eventCategory: 'data_access',
        timestamp: businessHoursDate,
        severity: 'medium',
        success: true
      }

      const pattern = {
        pattern_type: 'time',
        pattern_rules: { time_range: 'off_hours' }
      }

      const result = await securityService['checkTimePattern'](event, pattern, pattern.pattern_rules)

      expect(result).toBe(false)
    })
  })
})
