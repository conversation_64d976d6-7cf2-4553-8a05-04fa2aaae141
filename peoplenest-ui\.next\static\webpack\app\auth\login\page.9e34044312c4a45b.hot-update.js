"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(app-pages-browser)/./src/components/providers/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Form validation schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Password is required\").min(6, \"Password must be at least 6 characters\"),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean().default(false)\n});\nfunction LoginPage() {\n    var _errors_email, _errors_password;\n    _s();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginSuccess, setLoginSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, isAuthenticated, isLoading: authLoading } = (0,_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__.useAuth)();\n    const { register, handleSubmit, formState: { errors, isSubmitting }, watch, trigger } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: \"\",\n            password: \"\",\n            rememberMe: false\n        },\n        mode: \"onChange\"\n    });\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (isAuthenticated && !authLoading) {\n                router.push(\"/dashboard\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"LoginPage.useEffect.handleKeyDown\": (event)=>{\n                    // Alt + D to fill demo credentials in development\n                    if ( true && event.altKey && event.key === 'd') {\n                        event.preventDefault();\n                        const emailInput = document.querySelector('input[name=\"email\"]');\n                        const passwordInput = document.querySelector('input[name=\"password\"]');\n                        if (emailInput && passwordInput) {\n                            emailInput.value = '<EMAIL>';\n                            passwordInput.value = 'password123';\n                            emailInput.dispatchEvent(new Event('input', {\n                                bubbles: true\n                            }));\n                            passwordInput.dispatchEvent(new Event('input', {\n                                bubbles: true\n                            }));\n                        }\n                    }\n                }\n            }[\"LoginPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"LoginPage.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"LoginPage.useEffect\"];\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const onSubmit = async (data)=>{\n        try {\n            setLoginError(null);\n            setLoginSuccess(false);\n            const success = await login(data.email, data.password);\n            if (success) {\n                setLoginSuccess(true);\n                // Small delay to show success message before redirect\n                setTimeout(()=>{\n                    router.push(\"/dashboard\");\n                }, 1000);\n            } else {\n                setLoginError(\"Invalid email or password. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            setLoginError(\"An unexpected error occurred. Please try again.\");\n        }\n    };\n    // Watch form values for real-time validation feedback\n    const watchedEmail = watch(\"email\");\n    const watchedPassword = watch(\"password\");\n    // Show loading state during auth check\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            initial: {\n                                scale: 0.8\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            transition: {\n                                delay: 0.2,\n                                duration: 0.3\n                            },\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-foreground mb-2\",\n                            children: \"PeopleNest\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Enterprise HRMS Platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2 mt-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"SOC 2 Compliant\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: \"Enterprise Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            className: \"space-y-1 pb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-2xl font-semibold text-center\",\n                                    children: \"Welcome back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    className: \"text-center\",\n                                    children: \"Sign in to your account to continue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                loginSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                        className: \"border-green-200 bg-green-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                className: \"text-green-800\",\n                                                children: \"Login successful! Redirecting to dashboard...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                        variant: \"destructive\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                children: loginError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit(onSubmit),\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        ...register(\"email\"),\n                                                        type: \"email\",\n                                                        placeholder: \"Enter your email\",\n                                                        label: \"Email Address\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        error: (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        ...register(\"password\"),\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        placeholder: \"Enter your password\",\n                                                        label: \"Password\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                            disabled: isSubmitting,\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 27\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        error: (_errors_password = errors.password) === null || _errors_password === void 0 ? void 0 : _errors_password.message,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ...register(\"rememberMe\"),\n                                                            type: \"checkbox\",\n                                                            className: \"rounded border-gray-300 text-primary focus:ring-primary disabled:opacity-50\",\n                                                            disabled: isSubmitting\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Remember me\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed\",\n                                                    onClick: (e)=>e.preventDefault(),\n                                                    children: \"Forgot password? (Coming Soon)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full h-11 text-base font-medium\",\n                                            loading: isSubmitting,\n                                            disabled: isSubmitting || loginSuccess,\n                                            children: isSubmitting ? \"Signing in...\" : loginSuccess ? \"Success!\" : \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: \"Demo Credentials\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"h-6 px-2 text-xs\",\n                                                    onClick: ()=>{\n                                                        const form = document.querySelector('form');\n                                                        const emailInput = form === null || form === void 0 ? void 0 : form.querySelector('input[name=\"email\"]');\n                                                        const passwordInput = form === null || form === void 0 ? void 0 : form.querySelector('input[name=\"password\"]');\n                                                        if (emailInput && passwordInput) {\n                                                            emailInput.value = '<EMAIL>';\n                                                            passwordInput.value = 'password123';\n                                                            emailInput.dispatchEvent(new Event('input', {\n                                                                bubbles: true\n                                                            }));\n                                                            passwordInput.dispatchEvent(new Event('input', {\n                                                                bubbles: true\n                                                            }));\n                                                        }\n                                                    },\n                                                    disabled: isSubmitting,\n                                                    children: \"Quick Fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-xs text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Email:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" <EMAIL>\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Password:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" Any password (6+ characters)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-600 mt-2\",\n                                                    children: \"Development mode - any valid email/password combination will work\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-500 mt-1\",\n                                                    children: \"Tip: Press Alt + D to quick fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed\",\n                                                onClick: (e)=>e.preventDefault(),\n                                                children: \"Contact your administrator\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.8,\n                        duration: 0.3\n                    },\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: \"Protected by enterprise-grade security and encryption\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"p29mOUgvSEElJd5N0Zzs7sKc3vI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__.useAuth,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});