# Deep Research: Authentication Mechanisms and RBAC Integration

## Executive Summary

This comprehensive research analyzes authentication mechanisms and Role-Based Access Control (RBAC) integration for enterprise HRMS applications, with specific focus on the PeopleNest system. The research covers current implementation analysis, modern authentication patterns, security best practices, and recommendations for enhancement.

## Table of Contents

1. [Current System Analysis](#current-system-analysis)
2. [Authentication Mechanisms](#authentication-mechanisms)
3. [RBAC Implementation Patterns](#rbac-implementation-patterns)
4. [Security Architecture](#security-architecture)
5. [Best Practices & Standards](#best-practices--standards)
6. [Vulnerabilities & Mitigation](#vulnerabilities--mitigation)
7. [Integration Recommendations](#integration-recommendations)
8. [Implementation Roadmap](#implementation-roadmap)

---

## 1. Current System Analysis

### 1.1 Existing Authentication Architecture

**Backend Implementation:**
- **JWT-based Authentication**: Uses JSON Web Tokens with 24-hour access tokens and 7-day refresh tokens
- **Session Management**: Database-stored refresh tokens with IP and User-Agent tracking
- **Password Security**: bcrypt hashing with account lockout after 5 failed attempts (30-minute lockout)
- **Development Mode**: Mock authentication for development environment

**Frontend Implementation:**
- **React Context**: AuthProvider with centralized state management
- **Token Storage**: localStorage for token persistence
- **Route Protection**: RouteGuard component with permission/role-based access control
- **Auto-refresh**: Token refresh mechanism with automatic logout on failure

### 1.2 Current RBAC Structure

**Role Hierarchy (5 Tiers):**
```
super_admin → hr_admin → hr → manager → employee
```

**Permission System:**
- **Hierarchical Permissions**: Higher roles inherit lower role permissions
- **Granular Controls**: Specific permissions for different operations
- **Resource-based**: Permissions tied to specific resources (employees, departments, etc.)

**Current Roles & Permissions:**
```typescript
employee: ['employee_read_own', 'employee_update_own']
manager: [...employee, 'manager', 'employee_read_team', 'employee_update_team']
hr: [...manager, 'hr', 'employee_read_all', 'employee_update_all', 'department_read', 'position_read']
hr_admin: [...hr, 'hr_admin', 'department_write', 'position_write', 'user_management']
super_admin: [...hr_admin, 'super_admin', 'system_admin']
```

### 1.3 Security Features

**Current Security Measures:**
- JWT token verification with secret key
- Account lockout mechanism (5 attempts, 30-minute lockout)
- IP address and User-Agent tracking
- Last activity timestamp updates
- Encrypted PII storage in database
- Input validation and sanitization

**Missing Security Features:**
- Multi-Factor Authentication (MFA)
- Rate limiting implementation
- CORS configuration (commented out)
- Security headers (helmet middleware not active)
- Session invalidation on logout
- Password complexity requirements
- Audit logging for sensitive operations

---

## 2. Authentication Mechanisms

### 2.1 JWT vs Session-Based Authentication

**Current Implementation: JWT with Refresh Tokens**

**Advantages:**
- Stateless authentication
- Scalable across microservices
- Reduced database queries for authentication
- Mobile-friendly
- Cross-domain support

**Disadvantages:**
- Token revocation complexity
- Larger payload size
- Security risks if compromised
- No server-side session control

**Hybrid Approach (Recommended):**
```typescript
interface AuthTokens {
  accessToken: string;    // Short-lived (15-30 minutes)
  refreshToken: string;   // Long-lived (7-30 days)
  sessionId: string;      // Server-side session tracking
}
```

### 2.2 Modern Authentication Patterns

**1. OAuth 2.0 + OpenID Connect**
- Industry standard for enterprise SSO
- Support for multiple identity providers
- Standardized token format and flows

**2. SAML 2.0 Integration**
- Enterprise identity federation
- Single Sign-On (SSO) capabilities
- Compliance with enterprise security policies

**3. Multi-Factor Authentication (MFA)**
- TOTP (Time-based One-Time Password)
- SMS/Email verification
- Hardware security keys (FIDO2/WebAuthn)
- Biometric authentication

**4. Passwordless Authentication**
- Magic links via email
- WebAuthn/FIDO2 implementation
- Biometric authentication
- SMS-based authentication

### 2.3 Token Management Best Practices

**Access Token Security:**
```typescript
const accessTokenConfig = {
  algorithm: 'RS256',           // Use RSA instead of HMAC
  expiresIn: '15m',            // Short expiration
  issuer: 'peoplenest.com',    // Token issuer
  audience: 'peoplenest-api',  // Token audience
  notBefore: 0,                // Token not valid before
  jwtid: generateUUID(),       // Unique token ID
};
```

**Refresh Token Security:**
```typescript
const refreshTokenConfig = {
  httpOnly: true,              // Prevent XSS attacks
  secure: true,                // HTTPS only
  sameSite: 'strict',          // CSRF protection
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  path: '/api/auth/refresh',   // Specific path
};
```

---

## 3. RBAC Implementation Patterns

### 3.1 Advanced RBAC Models

**1. Hierarchical RBAC (Current)**
- Role inheritance structure
- Permission aggregation
- Simplified management

**2. Attribute-Based Access Control (ABAC)**
```typescript
interface AccessPolicy {
  subject: UserAttributes;     // Who
  resource: ResourceAttributes; // What
  action: string;              // How
  environment: ContextAttributes; // When/Where
  condition: PolicyCondition;  // Under what circumstances
}
```

**3. Dynamic RBAC**
- Context-aware permissions
- Time-based access control
- Location-based restrictions
- Device-based policies

### 3.2 Permission Granularity

**Resource-Level Permissions:**
```typescript
interface Permission {
  resource: string;            // 'employee', 'department', 'payroll'
  action: string;              // 'create', 'read', 'update', 'delete'
  scope: string;               // 'own', 'team', 'department', 'all'
  conditions?: Condition[];    // Additional constraints
}

// Examples:
'employee:read:own'           // Read own employee data
'employee:update:team'        // Update team member data
'payroll:read:department'     // Read department payroll
'system:admin:all'            // Full system administration
```

**Conditional Permissions:**
```typescript
interface ConditionalPermission {
  permission: string;
  conditions: {
    timeRange?: TimeRange;     // Business hours only
    ipRange?: string[];        // Specific IP addresses
    deviceType?: string[];     // Trusted devices only
    mfaRequired?: boolean;     // Require MFA for sensitive operations
    approvalRequired?: boolean; // Require approval workflow
  };
}
```

### 3.3 Role Management Strategies

**1. Role Templates**
```typescript
const roleTemplates = {
  'new_hire': ['employee_read_own', 'employee_update_own'],
  'team_lead': ['manager', 'employee_read_team', 'employee_update_team'],
  'hr_specialist': ['hr', 'employee_read_all', 'benefits_manage'],
  'payroll_admin': ['payroll_read_all', 'payroll_process', 'payroll_approve']
};
```

**2. Dynamic Role Assignment**
```typescript
interface RoleAssignmentRule {
  condition: string;           // 'department === "HR"'
  roles: string[];             // ['hr', 'employee']
  temporary?: boolean;         // Temporary assignment
  expiresAt?: Date;           // Expiration date
}
```

**3. Role Delegation**
```typescript
interface RoleDelegation {
  delegator: string;           // User delegating role
  delegatee: string;           // User receiving role
  roles: string[];             // Roles being delegated
  startDate: Date;             // When delegation starts
  endDate: Date;               // When delegation ends
  reason: string;              // Reason for delegation
}
```

---

## 4. Security Architecture

### 4.1 Defense in Depth Strategy

**Layer 1: Network Security**
- WAF (Web Application Firewall)
- DDoS protection
- IP whitelisting for admin access
- VPN requirements for sensitive operations

**Layer 2: Application Security**
- Input validation and sanitization
- Output encoding
- SQL injection prevention
- XSS protection

**Layer 3: Authentication Security**
- Strong password policies
- Multi-factor authentication
- Account lockout mechanisms
- Session management

**Layer 4: Authorization Security**
- Principle of least privilege
- Role-based access control
- Resource-level permissions
- Audit logging

**Layer 5: Data Security**
- Encryption at rest
- Encryption in transit
- PII data protection
- Secure key management

### 4.2 Zero Trust Architecture

**Core Principles:**
1. **Never Trust, Always Verify**: Authenticate and authorize every request
2. **Least Privilege Access**: Minimal necessary permissions
3. **Assume Breach**: Design for compromise scenarios
4. **Verify Explicitly**: Use multiple data sources for decisions

**Implementation:**
```typescript
interface ZeroTrustPolicy {
  identity: IdentityVerification;    // Who is the user?
  device: DeviceVerification;        // What device are they using?
  location: LocationVerification;    // Where are they connecting from?
  behavior: BehaviorAnalysis;        // Is this normal behavior?
  risk: RiskAssessment;             // What's the risk level?
}
```

### 4.3 Compliance Requirements

**SOC 2 Type II Compliance:**
- Access control monitoring
- Audit trail maintenance
- Data encryption requirements
- Incident response procedures

**GDPR Compliance:**
- Data subject rights
- Consent management
- Data portability
- Right to be forgotten

**HIPAA Compliance (if applicable):**
- PHI protection
- Access logging
- Encryption requirements
- Business associate agreements

---

## 5. Best Practices & Standards

### 5.1 Authentication Best Practices

**Password Security:**
```typescript
const passwordPolicy = {
  minLength: 12,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommonPasswords: true,
  preventUserInfoInPassword: true,
  maxAge: 90, // days
  historyCount: 12, // prevent reuse of last 12 passwords
};
```

**Session Management:**
```typescript
const sessionConfig = {
  maxConcurrentSessions: 3,      // Limit concurrent sessions
  idleTimeout: 30 * 60 * 1000,   // 30 minutes idle timeout
  absoluteTimeout: 8 * 60 * 60 * 1000, // 8 hours absolute timeout
  renewOnActivity: true,          // Extend session on activity
  secureTransport: true,          // HTTPS only
  httpOnly: true,                 // Prevent XSS
  sameSite: 'strict',            // CSRF protection
};
```

### 5.2 RBAC Best Practices

**Role Design Principles:**
1. **Business-Aligned**: Roles should match business functions
2. **Minimal Privilege**: Start with minimal permissions, add as needed
3. **Separation of Duties**: Prevent conflicts of interest
4. **Regular Review**: Periodic access reviews and cleanup

**Permission Naming Convention:**
```
{resource}:{action}:{scope}:{condition}

Examples:
employee:read:own:always
payroll:approve:department:business_hours
system:admin:all:mfa_required
```

### 5.3 Security Monitoring

**Audit Events:**
```typescript
interface AuditEvent {
  timestamp: Date;
  userId: string;
  sessionId: string;
  action: string;
  resource: string;
  result: 'success' | 'failure' | 'denied';
  ipAddress: string;
  userAgent: string;
  riskScore: number;
  metadata: Record<string, any>;
}
```

**Security Metrics:**
- Failed login attempts per user/IP
- Privilege escalation attempts
- Unusual access patterns
- Geographic anomalies
- Time-based anomalies

---

## 6. Vulnerabilities & Mitigation

### 6.1 Common Authentication Vulnerabilities

**1. JWT Vulnerabilities**
- **Algorithm Confusion**: Use RS256 instead of HS256
- **Token Leakage**: Implement secure storage
- **Replay Attacks**: Add jti (JWT ID) and nonce

**2. Session Vulnerabilities**
- **Session Fixation**: Regenerate session ID on login
- **Session Hijacking**: Use secure, httpOnly cookies
- **CSRF Attacks**: Implement CSRF tokens

**3. Password Vulnerabilities**
- **Weak Passwords**: Enforce strong password policies
- **Credential Stuffing**: Implement rate limiting and CAPTCHA
- **Password Spraying**: Monitor for distributed attacks

### 6.2 RBAC Vulnerabilities

**1. Privilege Escalation**
- **Horizontal**: Access to peer-level resources
- **Vertical**: Access to higher-privilege resources
- **Mitigation**: Strict permission validation, audit logging

**2. Role Mining Attacks**
- **Attack**: Analyzing role assignments to find vulnerabilities
- **Mitigation**: Regular role reviews, principle of least privilege

**3. Permission Creep**
- **Issue**: Accumulation of unnecessary permissions over time
- **Mitigation**: Automated access reviews, role lifecycle management

### 6.3 Mitigation Strategies

**Rate Limiting Implementation:**
```typescript
const rateLimits = {
  login: { requests: 5, window: 15 * 60 * 1000 },      // 5 attempts per 15 minutes
  api: { requests: 100, window: 15 * 60 * 1000 },      // 100 requests per 15 minutes
  sensitive: { requests: 10, window: 60 * 60 * 1000 }, // 10 sensitive ops per hour
};
```

**Anomaly Detection:**
```typescript
interface AnomalyDetection {
  geolocation: boolean;        // Unusual login locations
  timePattern: boolean;        // Unusual login times
  deviceFingerprint: boolean;  // New or unusual devices
  behaviorPattern: boolean;    // Unusual access patterns
  velocityCheck: boolean;      // Impossible travel scenarios
}
```

---

## 7. Integration Recommendations

### 7.1 Immediate Improvements (Phase 1)

**1. Enhanced Security Headers**
```typescript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", process.env.API_URL]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

**2. Rate Limiting Implementation**
```typescript
import rateLimit from 'express-rate-limit';

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/auth/login', authLimiter);
```

**3. CORS Configuration**
```typescript
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['https://app.peoplenest.com'],
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));
```

### 7.2 Medium-term Enhancements (Phase 2)

**1. Multi-Factor Authentication**
- TOTP implementation using libraries like `speakeasy`
- SMS verification integration
- Email-based verification
- Backup codes for recovery

**2. Advanced Session Management**
- Redis-based session storage
- Session invalidation on logout
- Concurrent session limits
- Device management

**3. Enhanced Audit Logging**
- Structured logging with correlation IDs
- Security event monitoring
- Real-time alerting for suspicious activities
- Compliance reporting

### 7.3 Long-term Strategic Improvements (Phase 3)

**1. SSO Integration**
- SAML 2.0 implementation
- OAuth 2.0/OpenID Connect
- Active Directory integration
- Third-party identity providers

**2. Zero Trust Architecture**
- Device trust verification
- Continuous authentication
- Risk-based access control
- Behavioral analytics

**3. Advanced RBAC Features**
- Attribute-based access control (ABAC)
- Dynamic role assignment
- Workflow-based approvals
- Delegation mechanisms

---

## 8. Implementation Roadmap

### Phase 1: Security Hardening (Weeks 1-2)
- [ ] Implement security headers (helmet)
- [ ] Configure CORS properly
- [ ] Add rate limiting
- [ ] Enhance password policies
- [ ] Implement session invalidation

### Phase 2: Authentication Enhancement (Weeks 3-6)
- [ ] Implement MFA (TOTP)
- [ ] Add device fingerprinting
- [ ] Enhance audit logging
- [ ] Implement anomaly detection
- [ ] Add password reset functionality

### Phase 3: RBAC Enhancement (Weeks 7-10)
- [ ] Implement granular permissions
- [ ] Add conditional access
- [ ] Create role management UI
- [ ] Implement access reviews
- [ ] Add delegation features

### Phase 4: Enterprise Integration (Weeks 11-16)
- [ ] SSO implementation (SAML/OAuth)
- [ ] Directory service integration
- [ ] Compliance reporting
- [ ] Advanced monitoring
- [ ] Zero trust features

---

## Conclusion

The current PeopleNest authentication and RBAC system provides a solid foundation with JWT-based authentication and hierarchical role management. However, significant enhancements are needed to meet enterprise security standards and compliance requirements.

Key priorities include implementing security hardening measures, adding multi-factor authentication, enhancing audit capabilities, and preparing for enterprise SSO integration. The phased approach ensures gradual improvement while maintaining system stability and user experience.

This research provides the foundation for building a robust, secure, and compliant authentication system suitable for enterprise HRMS applications.

---

## Appendix A: Code Implementation Examples

### A.1 Enhanced JWT Implementation

```typescript
// Enhanced JWT service with security best practices
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { promisify } from 'util';

interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  employeeId?: string;
  sessionId: string;
  deviceId: string;
  iat: number;
  exp: number;
  jti: string;
  iss: string;
  aud: string;
}

class SecureJWTService {
  private readonly accessTokenSecret: string;
  private readonly refreshTokenSecret: string;
  private readonly issuer: string;
  private readonly audience: string;

  constructor() {
    this.accessTokenSecret = process.env.JWT_ACCESS_SECRET!;
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET!;
    this.issuer = process.env.JWT_ISSUER || 'peoplenest.com';
    this.audience = process.env.JWT_AUDIENCE || 'peoplenest-api';
  }

  async generateAccessToken(payload: Omit<TokenPayload, 'iat' | 'exp' | 'jti' | 'iss' | 'aud'>): Promise<string> {
    const tokenPayload: TokenPayload = {
      ...payload,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (15 * 60), // 15 minutes
      jti: crypto.randomUUID(),
      iss: this.issuer,
      aud: this.audience,
    };

    return jwt.sign(tokenPayload, this.accessTokenSecret, {
      algorithm: 'RS256',
      keyid: 'access-key-1',
    });
  }

  async generateRefreshToken(userId: string, sessionId: string): Promise<string> {
    const payload = {
      userId,
      sessionId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
      jti: crypto.randomUUID(),
      iss: this.issuer,
      aud: this.audience,
    };

    return jwt.sign(payload, this.refreshTokenSecret, {
      algorithm: 'RS256',
      keyid: 'refresh-key-1',
    });
  }

  async verifyAccessToken(token: string): Promise<TokenPayload> {
    try {
      const decoded = jwt.verify(token, this.accessTokenSecret, {
        algorithms: ['RS256'],
        issuer: this.issuer,
        audience: this.audience,
      }) as TokenPayload;

      // Additional security checks
      if (!decoded.jti || !decoded.sessionId || !decoded.deviceId) {
        throw new Error('Invalid token structure');
      }

      return decoded;
    } catch (error) {
      throw new Error(`Token verification failed: ${error.message}`);
    }
  }

  async verifyRefreshToken(token: string): Promise<any> {
    try {
      return jwt.verify(token, this.refreshTokenSecret, {
        algorithms: ['RS256'],
        issuer: this.issuer,
        audience: this.audience,
      });
    } catch (error) {
      throw new Error(`Refresh token verification failed: ${error.message}`);
    }
  }

  async revokeToken(jti: string): Promise<void> {
    // Add token to blacklist (Redis implementation)
    await this.addToBlacklist(jti);
  }

  private async addToBlacklist(jti: string): Promise<void> {
    // Implementation depends on your caching solution (Redis, etc.)
    // This is a placeholder for the actual implementation
  }
}
```

### A.2 Advanced RBAC Middleware

```typescript
// Advanced RBAC middleware with context-aware permissions
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

interface AccessContext {
  user: User;
  resource: string;
  action: string;
  resourceId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  sessionId: string;
  deviceId: string;
}

interface AccessPolicy {
  permission: string;
  conditions?: {
    timeRestriction?: {
      startTime: string; // HH:MM format
      endTime: string;   // HH:MM format
      timezone: string;
    };
    ipRestriction?: {
      allowedRanges: string[];
      blockedRanges: string[];
    };
    deviceRestriction?: {
      trustedDevices: boolean;
      mfaRequired: boolean;
    };
    resourceRestriction?: {
      ownedOnly: boolean;
      departmentOnly: boolean;
      managerApprovalRequired: boolean;
    };
  };
}

class AdvancedRBACService {
  private policies: Map<string, AccessPolicy[]> = new Map();

  constructor() {
    this.loadPolicies();
  }

  private loadPolicies(): void {
    // Load policies from database or configuration
    this.policies.set('employee:read:sensitive', [{
      permission: 'employee:read:sensitive',
      conditions: {
        timeRestriction: {
          startTime: '09:00',
          endTime: '17:00',
          timezone: 'UTC'
        },
        deviceRestriction: {
          trustedDevices: true,
          mfaRequired: true
        }
      }
    }]);

    this.policies.set('payroll:process:all', [{
      permission: 'payroll:process:all',
      conditions: {
        timeRestriction: {
          startTime: '08:00',
          endTime: '18:00',
          timezone: 'UTC'
        },
        ipRestriction: {
          allowedRanges: ['10.0.0.0/8', '***********/16']
        },
        deviceRestriction: {
          trustedDevices: true,
          mfaRequired: true
        }
      }
    }]);
  }

  async checkAccess(context: AccessContext): Promise<boolean> {
    const permissionKey = `${context.resource}:${context.action}`;
    const policies = this.policies.get(permissionKey);

    if (!policies) {
      // Default permission check
      return this.hasBasicPermission(context.user, permissionKey);
    }

    for (const policy of policies) {
      if (await this.evaluatePolicy(policy, context)) {
        return true;
      }
    }

    return false;
  }

  private async evaluatePolicy(policy: AccessPolicy, context: AccessContext): Promise<boolean> {
    // Check basic permission first
    if (!this.hasBasicPermission(context.user, policy.permission)) {
      return false;
    }

    // Evaluate conditions
    if (policy.conditions) {
      if (policy.conditions.timeRestriction && !this.checkTimeRestriction(policy.conditions.timeRestriction, context)) {
        return false;
      }

      if (policy.conditions.ipRestriction && !this.checkIpRestriction(policy.conditions.ipRestriction, context)) {
        return false;
      }

      if (policy.conditions.deviceRestriction && !await this.checkDeviceRestriction(policy.conditions.deviceRestriction, context)) {
        return false;
      }

      if (policy.conditions.resourceRestriction && !this.checkResourceRestriction(policy.conditions.resourceRestriction, context)) {
        return false;
      }
    }

    return true;
  }

  private hasBasicPermission(user: User, permission: string): boolean {
    return user.permissions.includes(permission);
  }

  private checkTimeRestriction(restriction: any, context: AccessContext): boolean {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

    return currentTime >= restriction.startTime && currentTime <= restriction.endTime;
  }

  private checkIpRestriction(restriction: any, context: AccessContext): boolean {
    // Implementation for IP range checking
    // This would use a library like 'ip-range-check'
    return true; // Placeholder
  }

  private async checkDeviceRestriction(restriction: any, context: AccessContext): Promise<boolean> {
    if (restriction.trustedDevices) {
      // Check if device is in trusted devices list
      const isTrusted = await this.isDeviceTrusted(context.deviceId, context.user.id);
      if (!isTrusted) return false;
    }

    if (restriction.mfaRequired) {
      // Check if MFA was completed for this session
      const mfaCompleted = await this.isMfaCompleted(context.sessionId);
      if (!mfaCompleted) return false;
    }

    return true;
  }

  private checkResourceRestriction(restriction: any, context: AccessContext): boolean {
    if (restriction.ownedOnly && context.resourceId) {
      // Check if user owns the resource
      return this.isResourceOwner(context.user.id, context.resourceId);
    }

    if (restriction.departmentOnly && context.resourceId) {
      // Check if resource belongs to user's department
      return this.isResourceInDepartment(context.user.departmentId, context.resourceId);
    }

    return true;
  }

  private async isDeviceTrusted(deviceId: string, userId: string): Promise<boolean> {
    // Implementation to check trusted devices
    return true; // Placeholder
  }

  private async isMfaCompleted(sessionId: string): Promise<boolean> {
    // Implementation to check MFA status
    return true; // Placeholder
  }

  private isResourceOwner(userId: string, resourceId: string): boolean {
    // Implementation to check resource ownership
    return true; // Placeholder
  }

  private isResourceInDepartment(departmentId: string, resourceId: string): boolean {
    // Implementation to check department membership
    return true; // Placeholder
  }
}

// Enhanced RBAC middleware
export const advancedRBAC = (resource: string, action: string) => {
  const rbacService = new AdvancedRBACService();

  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    const context: AccessContext = {
      user: req.user,
      resource,
      action,
      resourceId: req.params.id || req.body.id,
      ipAddress: req.ip || req.connection.remoteAddress || '',
      userAgent: req.get('User-Agent') || '',
      timestamp: new Date(),
      sessionId: req.user.sessionId || '',
      deviceId: req.get('X-Device-ID') || '',
    };

    try {
      const hasAccess = await rbacService.checkAccess(context);

      if (!hasAccess) {
        logger.warn('Access denied - advanced RBAC check failed', {
          userId: req.user.id,
          resource,
          action,
          resourceId: context.resourceId,
          ipAddress: context.ipAddress,
          timestamp: context.timestamp
        });

        return res.status(403).json({
          error: 'Forbidden',
          message: 'Access denied due to policy restrictions'
        });
      }

      // Log successful access for audit
      logger.info('Access granted', {
        userId: req.user.id,
        resource,
        action,
        resourceId: context.resourceId,
        ipAddress: context.ipAddress,
        timestamp: context.timestamp
      });

      next();
    } catch (error) {
      logger.error('RBAC evaluation error', {
        error: error.message,
        userId: req.user.id,
        resource,
        action
      });

      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Access control evaluation failed'
      });
    }
  };
};
```

### A.3 Multi-Factor Authentication Implementation

```typescript
// MFA implementation with TOTP and backup codes
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import crypto from 'crypto';

interface MFASecret {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

interface MFAVerification {
  token: string;
  userId: string;
  sessionId: string;
}

class MFAService {
  async generateSecret(userId: string, email: string): Promise<MFASecret> {
    const secret = speakeasy.generateSecret({
      name: `PeopleNest (${email})`,
      issuer: 'PeopleNest HRMS',
      length: 32,
    });

    const qrCode = await QRCode.toDataURL(secret.otpauth_url!);
    const backupCodes = this.generateBackupCodes();

    // Store secret in database (encrypted)
    await this.storeMFASecret(userId, secret.base32, backupCodes);

    return {
      secret: secret.base32,
      qrCode,
      backupCodes,
    };
  }

  async verifyToken(verification: MFAVerification): Promise<boolean> {
    const userSecret = await this.getUserMFASecret(verification.userId);

    if (!userSecret) {
      throw new Error('MFA not configured for user');
    }

    // Verify TOTP token
    const verified = speakeasy.totp.verify({
      secret: userSecret,
      encoding: 'base32',
      token: verification.token,
      window: 2, // Allow 2 time steps (60 seconds) of drift
    });

    if (verified) {
      // Mark MFA as completed for this session
      await this.markMFACompleted(verification.sessionId);
      return true;
    }

    // Check if it's a backup code
    const isBackupCode = await this.verifyBackupCode(verification.userId, verification.token);
    if (isBackupCode) {
      await this.markMFACompleted(verification.sessionId);
      return true;
    }

    return false;
  }

  async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    const backupCodes = await this.getUserBackupCodes(userId);
    const hashedCode = crypto.createHash('sha256').update(code).digest('hex');

    const codeIndex = backupCodes.findIndex(bc => bc.hash === hashedCode && !bc.used);

    if (codeIndex !== -1) {
      // Mark backup code as used
      await this.markBackupCodeUsed(userId, codeIndex);
      return true;
    }

    return false;
  }

  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }

  private async storeMFASecret(userId: string, secret: string, backupCodes: string[]): Promise<void> {
    // Implementation to store encrypted MFA secret and backup codes
  }

  private async getUserMFASecret(userId: string): Promise<string | null> {
    // Implementation to retrieve user's MFA secret
    return null;
  }

  private async markMFACompleted(sessionId: string): Promise<void> {
    // Implementation to mark MFA as completed for session
  }

  private async getUserBackupCodes(userId: string): Promise<Array<{hash: string, used: boolean}>> {
    // Implementation to retrieve user's backup codes
    return [];
  }

  private async markBackupCodeUsed(userId: string, codeIndex: number): Promise<void> {
    // Implementation to mark backup code as used
  }
}

// MFA middleware
export const requireMFA = async (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required'
    });
  }

  const sessionId = req.user.sessionId;
  const mfaCompleted = await isMFACompletedForSession(sessionId);

  if (!mfaCompleted) {
    return res.status(403).json({
      error: 'MFA Required',
      message: 'Multi-factor authentication required',
      code: 'MFA_REQUIRED'
    });
  }

  next();
};

async function isMFACompletedForSession(sessionId: string): Promise<boolean> {
  // Implementation to check if MFA is completed for session
  return false;
}
```

---

## Appendix B: Database Schema Enhancements

### B.1 Enhanced User Sessions Table

```sql
-- Enhanced user sessions with device tracking and security features
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    refresh_token_hash VARCHAR(255) NOT NULL, -- Store hash, not actual token

    -- Device and location information
    device_id VARCHAR(255),
    device_fingerprint JSONB,
    ip_address INET NOT NULL,
    user_agent TEXT,
    geolocation JSONB, -- {country, region, city, lat, lng}

    -- Security flags
    is_trusted_device BOOLEAN DEFAULT false,
    mfa_completed BOOLEAN DEFAULT false,
    mfa_completed_at TIMESTAMP,
    risk_score INTEGER DEFAULT 0, -- 0-100 risk assessment

    -- Session lifecycle
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    invalidated_at TIMESTAMP,
    invalidation_reason VARCHAR(100), -- 'logout', 'timeout', 'security', 'admin'

    -- Audit fields
    created_by_ip INET,
    created_by_user_agent TEXT,

    CONSTRAINT valid_expiration CHECK (expires_at > created_at),
    CONSTRAINT valid_invalidation CHECK (invalidated_at IS NULL OR invalidated_at >= created_at)
);

-- Indexes for performance
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_device_id ON user_sessions(device_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_ip_address ON user_sessions(ip_address);
```

### B.2 Enhanced Permissions and Roles Schema

```sql
-- Granular permissions system
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL, -- e.g., 'employee:read:own'
    resource VARCHAR(50) NOT NULL,     -- e.g., 'employee'
    action VARCHAR(50) NOT NULL,       -- e.g., 'read'
    scope VARCHAR(50) NOT NULL,        -- e.g., 'own', 'team', 'all'
    description TEXT,
    is_sensitive BOOLEAN DEFAULT false,
    requires_mfa BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Role definitions with metadata
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    max_concurrent_users INTEGER, -- Limit for role-based licensing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Role-permission mapping with conditions
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,

    -- Conditional access
    conditions JSONB, -- Time, IP, device restrictions
    is_active BOOLEAN DEFAULT true,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID REFERENCES users(id),

    UNIQUE(role_id, permission_id)
);

-- User role assignments with temporal aspects
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,

    -- Temporal assignment
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,

    -- Assignment context
    assigned_by UUID REFERENCES users(id),
    assignment_reason TEXT,

    -- Delegation support
    is_delegated BOOLEAN DEFAULT false,
    delegated_by UUID REFERENCES users(id),
    delegation_expires_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT valid_assignment_period CHECK (expires_at IS NULL OR expires_at > assigned_at),
    CONSTRAINT valid_delegation CHECK (
        (is_delegated = false) OR
        (is_delegated = true AND delegated_by IS NOT NULL AND delegation_expires_at IS NOT NULL)
    )
);

-- Audit trail for role changes
CREATE TABLE role_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    role_id UUID NOT NULL REFERENCES roles(id),
    action VARCHAR(20) NOT NULL, -- 'granted', 'revoked', 'expired', 'delegated'
    previous_state JSONB,
    new_state JSONB,
    performed_by UUID REFERENCES users(id),
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    reason TEXT
);
```

### B.3 Security Audit Tables

```sql
-- Comprehensive audit logging
CREATE TABLE security_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Event identification
    event_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'permission_check', 'data_access'
    event_category VARCHAR(30) NOT NULL, -- 'authentication', 'authorization', 'data_access'
    severity VARCHAR(20) NOT NULL, -- 'info', 'warning', 'error', 'critical'

    -- User and session context
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(255),
    impersonated_by UUID REFERENCES users(id), -- For admin impersonation

    -- Request context
    resource VARCHAR(100),
    action VARCHAR(50),
    resource_id VARCHAR(255),
    endpoint VARCHAR(255),
    http_method VARCHAR(10),

    -- Network context
    ip_address INET,
    user_agent TEXT,
    geolocation JSONB,

    -- Result and details
    result VARCHAR(20) NOT NULL, -- 'success', 'failure', 'denied'
    error_code VARCHAR(50),
    error_message TEXT,
    risk_score INTEGER DEFAULT 0,

    -- Additional metadata
    metadata JSONB,
    duration_ms INTEGER,

    -- Timestamp
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Compliance fields
    retention_until TIMESTAMP WITH TIME ZONE, -- For data retention policies
    is_pii_access BOOLEAN DEFAULT false,
    compliance_tags TEXT[] -- ['SOC2', 'GDPR', 'HIPAA']
);

-- Indexes for audit queries
CREATE INDEX idx_security_audit_user_id ON security_audit_log(user_id);
CREATE INDEX idx_security_audit_timestamp ON security_audit_log(timestamp);
CREATE INDEX idx_security_audit_event_type ON security_audit_log(event_type);
CREATE INDEX idx_security_audit_ip_address ON security_audit_log(ip_address);
CREATE INDEX idx_security_audit_result ON security_audit_log(result);
CREATE INDEX idx_security_audit_severity ON security_audit_log(severity);

-- Failed login attempts tracking
CREATE TABLE failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255),
    ip_address INET NOT NULL,
    user_agent TEXT,
    attempt_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    failure_reason VARCHAR(100), -- 'invalid_password', 'account_locked', 'user_not_found'
    geolocation JSONB,

    -- Rate limiting support
    attempts_in_window INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_failed_login_email ON failed_login_attempts(email);
CREATE INDEX idx_failed_login_ip ON failed_login_attempts(ip_address);
CREATE INDEX idx_failed_login_time ON failed_login_attempts(attempt_time);
```

This comprehensive research document provides a deep analysis of authentication mechanisms and RBAC integration, covering current implementation, best practices, security considerations, and detailed implementation examples. The research serves as a foundation for enhancing the PeopleNest authentication system to meet enterprise security standards.
