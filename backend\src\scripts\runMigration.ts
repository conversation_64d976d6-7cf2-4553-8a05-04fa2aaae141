import { Pool } from 'pg'
import * as fs from 'fs'
import * as path from 'path'
import { logger } from '../utils/logger'

// Database configuration - use same as DatabaseService
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT || '5433'),
  database: process.env.DB_NAME || 'peoplenest',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'DeepAsha@2025!',
}

async function runMigration(migrationFile: string) {
  const pool = new Pool(dbConfig)
  
  try {
    logger.info(`Running migration: ${migrationFile}`)
    
    // Read migration file
    const migrationPath = path.join(__dirname, '..', 'migrations', migrationFile)
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Execute migration
    await pool.query(migrationSQL)
    
    logger.info(`Migration ${migrationFile} completed successfully`)
    
  } catch (error) {
    logger.error(`Migration ${migrationFile} failed:`, error)
    throw error
  } finally {
    await pool.end()
  }
}

// Run the MFA system migration
if (require.main === module) {
  const migrationFile = process.argv[2] || '008_mfa_system.sql'
  runMigration(migrationFile)
    .then(() => {
      console.log(`Migration ${migrationFile} completed successfully`)
      process.exit(0)
    })
    .catch((error) => {
      console.error('Migration failed:', error)
      process.exit(1)
    })
}

export { runMigration }
