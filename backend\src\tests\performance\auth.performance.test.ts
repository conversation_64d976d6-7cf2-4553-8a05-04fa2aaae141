import request from 'supertest'
import express from 'express'
import authRoutes from '../../routes/auth'
import { DatabaseService } from '../../services/databaseService'
import { AuthService } from '../../services/authService'
import { MFAService } from '../../services/mfaService'
import { AuditService } from '../../services/auditService'

// Mock dependencies for performance testing
jest.mock('../../services/databaseService')
jest.mock('../../services/authService')
jest.mock('../../services/mfaService')
jest.mock('../../services/auditService')
jest.mock('../../utils/logger')

const mockDb = {
  query: jest.fn()
}

const mockAuthService = {
  generateTokens: jest.fn(),
  verifyToken: jest.fn(),
  createSession: jest.fn()
}

const mockMFAService = {
  isMFARequired: jest.fn(),
  verifyMFAToken: jest.fn()
}

const mockAuditService = {
  logAuditEvent: jest.fn()
}

// Mock constructors
;(DatabaseService as jest.MockedClass<typeof DatabaseService>).mockImplementation(() => mockDb as any)
;(AuthService as jest.MockedClass<typeof AuthService>).mockImplementation(() => mockAuthService as any)
;(MFAService as jest.MockedClass<typeof MFAService>).mockImplementation(() => mockMFAService as any)
;(AuditService as jest.MockedClass<typeof AuditService>).mockImplementation(() => mockAuditService as any)

// Setup Express app for testing
const app = express()
app.use(express.json())
app.use('/auth', authRoutes)

describe('Authentication Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default mocks for successful operations
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      password_hash: 'hashed-password',
      role: 'employee',
      is_active: true,
      failed_login_attempts: 0,
      locked_until: null
    }

    const mockTokens = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token'
    }

    const mockSession = {
      id: 'session-123',
      userId: 'user-123'
    }

    mockDb.query.mockResolvedValue({ rows: [mockUser], rowCount: 1 })
    mockAuthService.generateTokens.mockResolvedValue(mockTokens)
    mockAuthService.createSession.mockResolvedValue(mockSession)
    mockMFAService.isMFARequired.mockResolvedValue(false)
    mockAuditService.logAuditEvent.mockResolvedValue(undefined)
  })

  describe('Login Performance', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
      deviceFingerprint: 'device-123'
    }

    it('should handle single login request within acceptable time', async () => {
      const startTime = Date.now()
      
      const response = await request(app)
        .post('/auth/login')
        .send(loginData)
      
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Login should complete within 500ms under normal conditions
      expect(responseTime).toBeLessThan(500)
      expect(response.status).toBeLessThan(500) // Should not be a server error
    })

    it('should handle concurrent login requests efficiently', async () => {
      const concurrentRequests = 10
      const startTime = Date.now()

      const requests = Array(concurrentRequests).fill(null).map((_, index) =>
        request(app)
          .post('/auth/login')
          .send({
            ...loginData,
            email: `test${index}@example.com`
          })
      )

      const responses = await Promise.all(requests)
      const endTime = Date.now()
      const totalTime = endTime - startTime

      // All concurrent requests should complete within 2 seconds
      expect(totalTime).toBeLessThan(2000)
      
      // All requests should be processed
      expect(responses).toHaveLength(concurrentRequests)
      
      // Calculate average response time
      const avgResponseTime = totalTime / concurrentRequests
      expect(avgResponseTime).toBeLessThan(200) // Average should be under 200ms
    })

    it('should handle high load login requests', async () => {
      const highLoadRequests = 50
      const batchSize = 10
      const batches = Math.ceil(highLoadRequests / batchSize)
      
      const startTime = Date.now()
      const allResponses: any[] = []

      // Process in batches to simulate realistic load
      for (let i = 0; i < batches; i++) {
        const batchRequests = Array(batchSize).fill(null).map((_, index) =>
          request(app)
            .post('/auth/login')
            .send({
              ...loginData,
              email: `load-test-${i}-${index}@example.com`
            })
        )

        const batchResponses = await Promise.all(batchRequests)
        allResponses.push(...batchResponses)
        
        // Small delay between batches to simulate realistic usage
        await new Promise(resolve => setTimeout(resolve, 10))
      }

      const endTime = Date.now()
      const totalTime = endTime - startTime

      // High load should complete within 10 seconds
      expect(totalTime).toBeLessThan(10000)
      expect(allResponses).toHaveLength(highLoadRequests)
      
      // Calculate throughput (requests per second)
      const throughput = (highLoadRequests / totalTime) * 1000
      expect(throughput).toBeGreaterThan(5) // Should handle at least 5 requests/second
    })
  })

  describe('Token Operations Performance', () => {
    it('should refresh tokens quickly', async () => {
      const refreshToken = 'valid-refresh-token'
      
      mockAuthService.verifyToken.mockResolvedValue({
        userId: 'user-123',
        type: 'refresh'
      })
      
      mockAuthService.generateTokens.mockResolvedValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token'
      })

      const startTime = Date.now()
      
      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Token refresh should be very fast (under 100ms)
      expect(responseTime).toBeLessThan(100)
      expect(response.status).toBeLessThan(500)
    })

    it('should handle concurrent token refresh requests', async () => {
      const concurrentRefreshes = 20
      
      mockAuthService.verifyToken.mockResolvedValue({
        userId: 'user-123',
        type: 'refresh'
      })
      
      mockAuthService.generateTokens.mockResolvedValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token'
      })

      const startTime = Date.now()

      const requests = Array(concurrentRefreshes).fill(null).map(() =>
        request(app)
          .post('/auth/refresh')
          .send({ refreshToken: 'valid-refresh-token' })
      )

      const responses = await Promise.all(requests)
      const endTime = Date.now()
      const totalTime = endTime - startTime

      // Concurrent refreshes should complete quickly
      expect(totalTime).toBeLessThan(1000)
      expect(responses).toHaveLength(concurrentRefreshes)
    })
  })

  describe('MFA Performance', () => {
    it('should verify MFA tokens efficiently', async () => {
      const mfaData = {
        tempToken: 'temp-token-123',
        mfaToken: '123456',
        deviceFingerprint: 'device-123'
      }

      mockAuthService.verifyToken.mockResolvedValue({
        userId: 'user-123',
        type: 'mfa_temp'
      })
      
      mockMFAService.verifyMFAToken.mockResolvedValue({
        success: true,
        method: 'totp'
      })

      const startTime = Date.now()
      
      const response = await request(app)
        .post('/auth/mfa/verify')
        .send(mfaData)
      
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // MFA verification should be fast (under 300ms)
      expect(responseTime).toBeLessThan(300)
      expect(response.status).toBeLessThan(500)
    })
  })

  describe('Database Performance', () => {
    it('should handle database queries efficiently', async () => {
      let queryCount = 0
      const originalQuery = mockDb.query
      
      // Count database queries
      mockDb.query.mockImplementation((...args) => {
        queryCount++
        return originalQuery(...args)
      })

      await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          deviceFingerprint: 'device-123'
        })

      // Login should not require excessive database queries
      expect(queryCount).toBeLessThan(10) // Should be efficient with queries
    })
  })

  describe('Memory Usage', () => {
    it('should not leak memory during multiple operations', async () => {
      const initialMemory = process.memoryUsage()
      
      // Perform multiple operations
      const operations = Array(100).fill(null).map((_, index) =>
        request(app)
          .post('/auth/login')
          .send({
            email: `memory-test-${index}@example.com`,
            password: 'password123',
            deviceFingerprint: `device-${index}`
          })
      )

      await Promise.all(operations)
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
    })
  })

  describe('Error Handling Performance', () => {
    it('should handle authentication failures quickly', async () => {
      // Mock authentication failure
      mockDb.query.mockResolvedValueOnce({ rows: [] }) // User not found

      const startTime = Date.now()
      
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
          deviceFingerprint: 'device-123'
        })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Error responses should also be fast
      expect(responseTime).toBeLessThan(200)
      expect(response.status).toBe(401)
    })
  })
})
