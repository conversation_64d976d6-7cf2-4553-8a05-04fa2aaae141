import express from 'express'
import { body, validationResult } from 'express-validator'
import { authMiddleware } from '../middleware/auth'
import { 
  requireResourcePermission,
  attachUserPermissions
} from '../middleware/enhancedRBAC'
import { auditMiddleware } from '../middleware/audit'
import { mfaService } from '../services/mfaService'
import { logger } from '../utils/logger'

const router = express.Router()

// Apply authentication and permission attachment to all routes
router.use(authMiddleware)
router.use(attachUserPermissions)

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * GET /mfa/status
 * Get MFA status for current user
 */
router.get('/status',
  auditMiddleware('mfa_status_check', 'data_access'),
  async (req, res) => {
    try {
      const status = await mfaService.getMFAStatus(req.user.id)
      const isRequired = await mfaService.isMFARequired(req.user.id)

      res.json({
        success: true,
        data: {
          ...status,
          required: isRequired
        }
      })

    } catch (error) {
      logger.error('Failed to get MFA status:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to get MFA status'
      })
    }
  }
)

/**
 * POST /mfa/setup
 * Generate MFA secret and QR code for setup
 */
router.post('/setup',
  [
    body('email').isEmail().withMessage('Valid email is required')
  ],
  validateRequest,
  auditMiddleware('mfa_setup_initiated', 'security_action'),
  async (req, res) => {
    try {
      const { email } = req.body

      // Verify email matches current user
      if (email !== req.user.email) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Email must match current user'
        })
      }

      const mfaSecret = await mfaService.generateMFASecret(req.user.id, email)

      res.json({
        success: true,
        message: 'MFA setup initiated',
        data: {
          qrCodeUrl: mfaSecret.qrCodeUrl,
          manualEntryKey: mfaSecret.manualEntryKey,
          backupCodes: mfaSecret.backupCodes
        }
      })

    } catch (error) {
      logger.error('Failed to setup MFA:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to setup MFA'
      })
    }
  }
)

/**
 * POST /mfa/verify-setup
 * Verify MFA token and complete setup
 */
router.post('/verify-setup',
  [
    body('token').isLength({ min: 6, max: 6 }).withMessage('Token must be 6 digits')
  ],
  validateRequest,
  auditMiddleware('mfa_setup_verification', 'security_action'),
  async (req, res) => {
    try {
      const { token } = req.body

      const verified = await mfaService.verifyAndEnableMFA(
        req.user.id, 
        token, 
        req.user.sessionId
      )

      if (!verified) {
        return res.status(400).json({
          error: 'Invalid Token',
          message: 'The provided token is invalid or expired'
        })
      }

      res.json({
        success: true,
        message: 'MFA enabled successfully',
        data: {
          enabled: true,
          verified: true
        }
      })

    } catch (error) {
      logger.error('Failed to verify MFA setup:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to verify MFA setup'
      })
    }
  }
)

/**
 * POST /mfa/verify
 * Verify MFA token for authentication
 */
router.post('/verify',
  [
    body('token').isLength({ min: 6, max: 10 }).withMessage('Token must be 6-10 characters')
  ],
  validateRequest,
  auditMiddleware('mfa_verification', 'authentication'),
  async (req, res) => {
    try {
      const { token } = req.body

      const result = await mfaService.verifyMFAToken(
        req.user.id, 
        token, 
        req.user.sessionId
      )

      if (!result.verified) {
        return res.status(400).json({
          error: 'Invalid Token',
          message: 'The provided token is invalid'
        })
      }

      const response: any = {
        success: true,
        message: 'MFA verification successful',
        data: {
          verified: true
        }
      }

      if (result.backupCodeUsed) {
        response.data.backupCodeUsed = true
        response.data.remainingBackupCodes = result.remainingBackupCodes
        
        if (result.remainingBackupCodes === 0) {
          response.warning = 'All backup codes have been used. Please generate new ones.'
        } else if (result.remainingBackupCodes <= 2) {
          response.warning = `Only ${result.remainingBackupCodes} backup codes remaining.`
        }
      }

      res.json(response)

    } catch (error) {
      logger.error('Failed to verify MFA token:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to verify MFA token'
      })
    }
  }
)

/**
 * POST /mfa/regenerate-backup-codes
 * Generate new backup codes
 */
router.post('/regenerate-backup-codes',
  auditMiddleware('mfa_backup_codes_regenerated', 'security_action'),
  async (req, res) => {
    try {
      const backupCodes = await mfaService.regenerateBackupCodes(req.user.id)

      res.json({
        success: true,
        message: 'Backup codes regenerated successfully',
        data: {
          backupCodes
        }
      })

    } catch (error) {
      logger.error('Failed to regenerate backup codes:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to regenerate backup codes'
      })
    }
  }
)

/**
 * DELETE /mfa/disable
 * Disable MFA for current user
 */
router.delete('/disable',
  [
    body('token').isLength({ min: 6, max: 10 }).withMessage('Token required for MFA disable')
  ],
  validateRequest,
  auditMiddleware('mfa_disabled', 'security_action'),
  async (req, res) => {
    try {
      const { token } = req.body

      // Verify current MFA token before disabling
      const verification = await mfaService.verifyMFAToken(req.user.id, token)
      
      if (!verification.verified) {
        return res.status(400).json({
          error: 'Invalid Token',
          message: 'Valid MFA token required to disable MFA'
        })
      }

      const disabled = await mfaService.disableMFA(req.user.id)

      if (!disabled) {
        return res.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to disable MFA'
        })
      }

      res.json({
        success: true,
        message: 'MFA disabled successfully',
        data: {
          enabled: false
        }
      })

    } catch (error) {
      logger.error('Failed to disable MFA:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to disable MFA'
      })
    }
  }
)

/**
 * GET /mfa/users
 * Get MFA status for all users (Admin only)
 */
router.get('/users',
  requireResourcePermission('users', 'read', { scope: 'all' }),
  auditMiddleware('mfa_users_list', 'data_access'),
  async (req, res) => {
    try {
      // This would require a new method in MFAService to get all users' MFA status
      // For now, return a placeholder response
      res.json({
        success: true,
        message: 'MFA user overview not yet implemented',
        data: []
      })

    } catch (error) {
      logger.error('Failed to get MFA users overview:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to get MFA users overview'
      })
    }
  }
)

/**
 * POST /mfa/admin/disable/:userId
 * Admin disable MFA for specific user
 */
router.post('/admin/disable/:userId',
  requireResourcePermission('users', 'manage', { scope: 'all' }),
  [
    body('reason').isLength({ min: 10 }).withMessage('Reason required (minimum 10 characters)')
  ],
  validateRequest,
  auditMiddleware('mfa_admin_disabled', 'administrative_action'),
  async (req, res) => {
    try {
      const { userId } = req.params
      const { reason } = req.body

      const disabled = await mfaService.disableMFA(userId, req.user.id)

      if (!disabled) {
        return res.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to disable MFA'
        })
      }

      res.json({
        success: true,
        message: 'MFA disabled by administrator',
        data: {
          userId,
          disabledBy: req.user.id,
          reason
        }
      })

    } catch (error) {
      logger.error('Failed to admin disable MFA:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to disable MFA'
      })
    }
  }
)

export default router
