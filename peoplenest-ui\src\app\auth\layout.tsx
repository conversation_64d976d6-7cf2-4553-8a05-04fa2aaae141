import type { Metadata } from "next"
import { AuthLayoutClient } from "./auth-layout-client"

export const metadata: Metadata = {
  title: "Authentication - PeopleNest HRMS",
  description: "Sign in to your PeopleNest HRMS account",
  robots: {
    index: false,
    follow: false,
  },
}

interface AuthLayoutProps {
  children: React.ReactNode
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return <AuthLayoutClient>{children}</AuthLayoutClient>
}
