'use strict';

module.exports = {
  ENVIRONMENT_CONFIG_MAP: {
    JEST_SUITE_NAME: 'suiteName',
    JEST_JUNIT_OUTPUT_DIR: 'outputDirectory',
    JEST_JUNIT_OUTPUT_NAME: 'outputName',
    JEST_JUNIT_OUTPUT_FILE: 'outputFile',
    JEST_JUNIT_UNIQUE_OUTPUT_NAME: 'uniqueOutputName',
    JEST_JUNIT_CLASSNAME: 'classNameTemplate',
    JEST_JUNIT_SUITE_NAME: 'suiteNameTemplate',
    JEST_JUNIT_TITLE: 'titleTemplate',
    JEST_JUNIT_ANCESTOR_SEPARATOR: 'ancestorSeparator',
    JEST_JUNIT_ADD_FILE_ATTRIBUTE: 'addFileAttribute',
    JEST_JUNIT_FILE_PATH_PREFIX: 'filePathPrefix',
    JEST_JUNIT_INCLUDE_CONSOLE_OUTPUT: 'includeConsoleOutput',
    JEST_JUNIT_INCLUDE_SHORT_CONSOLE_OUTPUT: 'includeShortConsoleOutput',
    JEST_JUNIT_REPORT_TEST_SUITE_ERRORS: 'reportTestSuiteErrors',
    JEST_JUNIT_NO_STACK_TRACE: "noStackTrace",
    JEST_USE_PATH_FOR_SUITE_NAME: 'usePathForSuiteName',
    JEST_JUNIT_TEST_CASE_PROPERTIES_JSON_FILE: 'testCasePropertiesFile',
    JEST_JUNIT_TEST_CASE_PROPERTIES_DIR: 'testCasePropertiesDirectory',
    JEST_JUNIT_TEST_SUITE_PROPERTIES_JSON_FILE: 'testSuitePropertiesFile',
    JEST_JUNIT_TEST_SUITE_PROPERTIES_DIR: 'testSuitePropertiesDirectory',
  },
  DEFAULT_OPTIONS: {
    suiteName: 'jest tests',
    outputDirectory: process.cwd(),
    outputName: 'junit.xml',
    uniqueOutputName: 'false',
    classNameTemplate: '{classname} {title}',
    suiteNameTemplate: '{title}',
    titleTemplate: '{classname} {title}',
    ancestorSeparator: ' ',
    usePathForSuiteName: 'false',
    addFileAttribute: 'false',
    filePathPrefix: '',
    includeConsoleOutput: 'false',
    includeShortConsoleOutput: 'false',
    reportTestSuiteErrors: 'false',
    noStackTrace: 'false',
    testCasePropertiesFile: 'junitTestCaseProperties.js',
    testCasePropertiesDirectory: process.cwd(),
    testSuitePropertiesFile: 'junitProperties.js',
    testSuitePropertiesDirectory: process.cwd(),
  },
  SUITENAME_VAR: 'suitename',
  CLASSNAME_VAR: 'classname',
  FILENAME_VAR: 'filename',
  FILEPATH_VAR: 'filepath',
  TITLE_VAR: 'title',
  DISPLAY_NAME_VAR: 'displayName',
};
