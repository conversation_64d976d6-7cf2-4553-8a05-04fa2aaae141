/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/auth/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/auth-layout-client.tsx */ \"(app-pages-browser)/./src/app/auth/auth-layout-client.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Blb3BsZU5lc3QlNUMlNUNwZW9wbGVuZXN0LXVpJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2F1dGgtbGF5b3V0LWNsaWVudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoTGF5b3V0Q2xpZW50JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoTGF5b3V0Q2xpZW50XCJdICovIFwiQzpcXFxcUGVvcGxlTmVzdFxcXFxwZW9wbGVuZXN0LXVpXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxhdXRoLWxheW91dC1jbGllbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxQZW9wbGVOZXN0XFxwZW9wbGVuZXN0LXVpXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/auth/auth-layout-client.tsx":
/*!*********************************************!*\
  !*** ./src/app/auth/auth-layout-client.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthLayoutClient: () => (/* binding */ AuthLayoutClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(app-pages-browser)/./src/components/providers/auth-provider.tsx\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(app-pages-browser)/./src/components/providers/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ AuthLayoutClient auto */ \n\n\nfunction AuthLayoutClient(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"absolute bottom-4 left-0 right-0 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"\\xa9 2024 PeopleNest. All rights reserved. | Privacy Policy | Terms of Service\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = AuthLayoutClient;\nvar _c;\n$RefreshReg$(_c, \"AuthLayoutClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/auth-layout-client.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        isAuthenticated: false,\n        isLoading: true,\n        token: null\n    });\n    // Initialize auth state from storage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        const storedToken = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                        const storedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredUser)();\n                        if (storedToken && storedUser) {\n                            // Verify token is still valid by calling /me endpoint\n                            const isValid = await verifyToken(storedToken);\n                            if (isValid) {\n                                setAuthState({\n                                    user: storedUser,\n                                    isAuthenticated: true,\n                                    isLoading: false,\n                                    token: storedToken\n                                });\n                            } else {\n                                // Token is invalid, clear storage\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.removeStoredToken)();\n                                setAuthState({\n                                    user: null,\n                                    isAuthenticated: false,\n                                    isLoading: false,\n                                    token: null\n                                });\n                            }\n                        } else {\n                            // For development, use mock user if no auth found\n                            if (true) {\n                                setAuthState({\n                                    user: _lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER,\n                                    isAuthenticated: true,\n                                    isLoading: false,\n                                    token: 'mock-token'\n                                });\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER);\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)('mock-token');\n                            } else {}\n                        }\n                    } catch (error) {\n                        console.error('Failed to initialize auth:', error);\n                        setAuthState({\n                            user: null,\n                            isAuthenticated: false,\n                            isLoading: false,\n                            token: null\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const verifyToken = async (token)=>{\n        try {\n            // Skip verification in development mode\n            if (true) {\n                return true;\n            }\n            const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL).concat(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.ME), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.error('Token verification failed:', error);\n            return false;\n        }\n    };\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[login]\": async (email, password)=>{\n            try {\n                setAuthState({\n                    \"AuthProvider.useCallback[login]\": (prev)=>({\n                            ...prev,\n                            isLoading: true\n                        })\n                }[\"AuthProvider.useCallback[login]\"]);\n                // In development mode, simulate login\n                if (true) {\n                    // Simulate API delay\n                    await new Promise({\n                        \"AuthProvider.useCallback[login]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"AuthProvider.useCallback[login]\"]);\n                    const mockToken = 'mock-jwt-token';\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(mockToken);\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER);\n                    setAuthState({\n                        user: _lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        token: mockToken\n                    });\n                    return true;\n                }\n                const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL).concat(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.LOGIN), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    const { token, user } = data;\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(token);\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n                    setAuthState({\n                        user,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        token\n                    });\n                    return true;\n                } else {\n                    setAuthState({\n                        \"AuthProvider.useCallback[login]\": (prev)=>({\n                                ...prev,\n                                isLoading: false\n                            })\n                    }[\"AuthProvider.useCallback[login]\"]);\n                    return false;\n                }\n            } catch (error) {\n                console.error('Login failed:', error);\n                setAuthState({\n                    \"AuthProvider.useCallback[login]\": (prev)=>({\n                            ...prev,\n                            isLoading: false\n                        })\n                }[\"AuthProvider.useCallback[login]\"]);\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[login]\"], []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.removeStoredToken)();\n            setAuthState({\n                user: null,\n                isAuthenticated: false,\n                isLoading: false,\n                token: null\n            });\n            // Redirect to login page\n            if (true) {\n                window.location.href = '/auth/login';\n            }\n        }\n    }[\"AuthProvider.useCallback[logout]\"], []);\n    const refreshToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshToken]\": async ()=>{\n            try {\n                const currentToken = authState.token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                if (!currentToken) {\n                    return false;\n                }\n                // Skip refresh in development mode\n                if (true) {\n                    return true;\n                }\n                const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL).concat(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.REFRESH), {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(currentToken),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    const { token, user } = data;\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(token);\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n                    setAuthState({\n                        \"AuthProvider.useCallback[refreshToken]\": (prev)=>({\n                                ...prev,\n                                user,\n                                token\n                            })\n                    }[\"AuthProvider.useCallback[refreshToken]\"]);\n                    return true;\n                } else {\n                    logout();\n                    return false;\n                }\n            } catch (error) {\n                console.error('Token refresh failed:', error);\n                logout();\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshToken]\"], [\n        authState.token,\n        logout\n    ]);\n    const updateUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updateUser]\": (user)=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n            setAuthState({\n                \"AuthProvider.useCallback[updateUser]\": (prev)=>({\n                        ...prev,\n                        user\n                    })\n            }[\"AuthProvider.useCallback[updateUser]\"]);\n        }\n    }[\"AuthProvider.useCallback[updateUser]\"], []);\n    const value = {\n        ...authState,\n        login,\n        logout,\n        refreshToken,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"QH1nOlaJm4N2l9v4gfVez95Pv9s=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/auth-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(app-pages-browser)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Provide a fallback when used outside of ThemeProvider\n        return {\n            theme: 'system',\n            setTheme: ()=>{},\n            toggleTheme: ()=>{},\n            systemTheme: 'light',\n            actualTheme: 'light'\n        };\n    }\n    return context;\n}\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction ThemeProvider(param) {\n    let { children, defaultTheme = 'system' } = param;\n    _s1();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [systemTheme, setSystemTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get the actual theme being applied\n    const actualTheme = theme === 'system' ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Initialize theme on mount\n            const storedTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)();\n            setThemeState(storedTheme);\n            // Detect system theme\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n            // Set mounted first to prevent hydration mismatch\n            setMounted(true);\n            // Note: Theme is already applied by the script in root layout\n            // We don't need to apply it again here to prevent hydration issues\n            // Listen for system theme changes\n            const handleSystemThemeChange = {\n                \"ThemeProvider.useEffect.handleSystemThemeChange\": (e)=>{\n                    setSystemTheme(e.matches ? 'dark' : 'light');\n                    // If using system theme, reapply it\n                    if ((0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)() === 'system') {\n                        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)('system');\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleSystemThemeChange\"];\n            mediaQuery.addEventListener('change', handleSystemThemeChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleSystemThemeChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const handleSetTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.setThemeMode)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n    };\n    const handleToggleTheme = ()=>{\n        const currentActualTheme = theme === 'system' ? systemTheme : theme;\n        const newTheme = currentActualTheme === 'light' ? 'dark' : 'light';\n        handleSetTheme(newTheme);\n    };\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                visibility: 'hidden'\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n            lineNumber: 89,\n            columnNumber: 12\n        }, this);\n    }\n    const value = {\n        theme,\n        setTheme: handleSetTheme,\n        toggleTheme: handleToggleTheme,\n        systemTheme,\n        actualTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s1(ThemeProvider, \"aR/n1RSeJUS4S0gjtRg+W2knBo8=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/theme-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ENDPOINTS: () => (/* binding */ AUTH_ENDPOINTS),\n/* harmony export */   MOCK_USER: () => (/* binding */ MOCK_USER),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   TOKEN_KEY: () => (/* binding */ TOKEN_KEY),\n/* harmony export */   USER_KEY: () => (/* binding */ USER_KEY),\n/* harmony export */   canAccessAdminModule: () => (/* binding */ canAccessAdminModule),\n/* harmony export */   canAccessHRModule: () => (/* binding */ canAccessHRModule),\n/* harmony export */   canAccessOrganizationModule: () => (/* binding */ canAccessOrganizationModule),\n/* harmony export */   canManageUsers: () => (/* binding */ canManageUsers),\n/* harmony export */   canReadAllEmployees: () => (/* binding */ canReadAllEmployees),\n/* harmony export */   canReadDepartments: () => (/* binding */ canReadDepartments),\n/* harmony export */   canReadPositions: () => (/* binding */ canReadPositions),\n/* harmony export */   canWriteAllEmployees: () => (/* binding */ canWriteAllEmployees),\n/* harmony export */   canWriteDepartments: () => (/* binding */ canWriteDepartments),\n/* harmony export */   canWritePositions: () => (/* binding */ canWritePositions),\n/* harmony export */   getStoredToken: () => (/* binding */ getStoredToken),\n/* harmony export */   getStoredUser: () => (/* binding */ getStoredUser),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasAnyRole: () => (/* binding */ hasAnyRole),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isRoleAtLeast: () => (/* binding */ isRoleAtLeast),\n/* harmony export */   removeStoredToken: () => (/* binding */ removeStoredToken),\n/* harmony export */   setStoredToken: () => (/* binding */ setStoredToken),\n/* harmony export */   setStoredUser: () => (/* binding */ setStoredUser)\n/* harmony export */ });\n// Authentication and authorization utilities\n// Permission hierarchy - higher roles inherit lower role permissions\nconst ROLE_PERMISSIONS = {\n    employee: [\n        'employee_read_own',\n        'employee_update_own'\n    ],\n    manager: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team'\n    ],\n    hr: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read'\n    ],\n    hr_admin: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read',\n        'hr_admin',\n        'department_write',\n        'position_write',\n        'user_management'\n    ],\n    super_admin: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read',\n        'hr_admin',\n        'department_write',\n        'position_write',\n        'user_management',\n        'super_admin',\n        'system_admin'\n    ]\n};\n// Token management\nconst TOKEN_KEY = 'peoplenest_auth_token';\nconst USER_KEY = 'peoplenest_user';\nfunction getStoredToken() {\n    if (false) {}\n    return localStorage.getItem(TOKEN_KEY);\n}\nfunction setStoredToken(token) {\n    if (false) {}\n    localStorage.setItem(TOKEN_KEY, token);\n}\nfunction removeStoredToken() {\n    if (false) {}\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(USER_KEY);\n}\nfunction getStoredUser() {\n    if (false) {}\n    const userStr = localStorage.getItem(USER_KEY);\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch (e) {\n        return null;\n    }\n}\nfunction setStoredUser(user) {\n    if (false) {}\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\n}\n// Permission checking utilities\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasRole(user, role) {\n    if (!user) return false;\n    return user.role === role;\n}\nfunction hasAnyRole(user, roles) {\n    if (!user) return false;\n    return roles.includes(user.role);\n}\nfunction isRoleAtLeast(user, minRole) {\n    if (!user) return false;\n    const roleHierarchy = [\n        'employee',\n        'manager',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ];\n    const userRoleIndex = roleHierarchy.indexOf(user.role);\n    const minRoleIndex = roleHierarchy.indexOf(minRole);\n    return userRoleIndex >= minRoleIndex;\n}\n// Department and organization permissions\nfunction canReadDepartments(user) {\n    return hasAnyPermission(user, [\n        'department_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWriteDepartments(user) {\n    return hasAnyPermission(user, [\n        'department_write',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canReadPositions(user) {\n    return hasAnyPermission(user, [\n        'position_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWritePositions(user) {\n    return hasAnyPermission(user, [\n        'position_write',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canReadAllEmployees(user) {\n    return hasAnyPermission(user, [\n        'employee_read_all',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWriteAllEmployees(user) {\n    return hasAnyPermission(user, [\n        'employee_update_all',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canManageUsers(user) {\n    return hasAnyPermission(user, [\n        'user_management',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\n// Navigation permissions\nfunction canAccessOrganizationModule(user) {\n    return hasAnyPermission(user, [\n        'department_read',\n        'position_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canAccessHRModule(user) {\n    return hasAnyPermission(user, [\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canAccessAdminModule(user) {\n    return hasAnyPermission(user, [\n        'hr_admin',\n        'super_admin'\n    ]);\n}\n// Mock user for development (remove in production)\nconst MOCK_USER = {\n    id: '1',\n    employeeId: 'EMP001',\n    email: '<EMAIL>',\n    firstName: 'Admin',\n    lastName: 'User',\n    role: 'hr_admin',\n    permissions: ROLE_PERMISSIONS.hr_admin,\n    departmentId: '1',\n    managerId: undefined\n};\n// API endpoints\nconst AUTH_ENDPOINTS = {\n    LOGIN: '/api/auth/login',\n    LOGOUT: '/api/auth/logout',\n    REFRESH: '/api/auth/refresh',\n    ME: '/api/auth/me'\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getContrastTextColor: () => (/* binding */ getContrastTextColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getThemeMode: () => (/* binding */ getThemeMode),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   setThemeMode: () => (/* binding */ setThemeMode),\n/* harmony export */   statusColors: () => (/* binding */ statusColors),\n/* harmony export */   themeColors: () => (/* binding */ themeColors),\n/* harmony export */   toggleTheme: () => (/* binding */ toggleTheme),\n/* harmony export */   useSystemTheme: () => (/* binding */ useSystemTheme)\n/* harmony export */ });\n// Theme utilities for PeopleNest HRMS\n// Provides consistent color management and theme switching capabilities\nconst themeColors = {\n    // Light mode colors\n    light: {\n        background: '#ffffff',\n        foreground: '#171717',\n        card: '#ffffff',\n        cardForeground: '#171717',\n        popover: '#ffffff',\n        popoverForeground: '#171717',\n        primary: '#3b82f6',\n        primaryForeground: '#ffffff',\n        secondary: '#f1f5f9',\n        secondaryForeground: '#0f172a',\n        muted: '#f1f5f9',\n        mutedForeground: '#64748b',\n        accent: '#f1f5f9',\n        accentForeground: '#0f172a',\n        destructive: '#ef4444',\n        destructiveForeground: '#ffffff',\n        border: '#e2e8f0',\n        input: '#e2e8f0',\n        ring: '#3b82f6',\n        // Enhanced semantic colors\n        textPrimary: '#171717',\n        textSecondary: '#64748b',\n        textTertiary: '#94a3b8',\n        textInverse: '#ffffff',\n        surfacePrimary: '#ffffff',\n        surfaceSecondary: '#f8fafc',\n        surfaceTertiary: '#f1f5f9',\n        surfaceHover: '#f8fafc',\n        surfaceActive: '#e2e8f0',\n        success: '#22c55e',\n        successForeground: '#ffffff',\n        warning: '#f59e0b',\n        warningForeground: '#ffffff',\n        info: '#3b82f6',\n        infoForeground: '#ffffff',\n        // Chart colors\n        chart1: '#3b82f6',\n        chart2: '#10b981',\n        chart3: '#f59e0b',\n        chart4: '#ef4444',\n        chart5: '#8b5cf6',\n        chart6: '#06b6d4',\n        chart7: '#84cc16',\n        chart8: '#f97316',\n        // Status colors\n        statusMeeting: '#3b82f6',\n        statusDeadline: '#ef4444',\n        statusEvent: '#10b981',\n        statusActive: '#22c55e',\n        statusPending: '#f59e0b',\n        statusInactive: '#6b7280'\n    },\n    // Dark mode colors\n    dark: {\n        background: '#0a0a0a',\n        foreground: '#ededed',\n        card: '#111111',\n        cardForeground: '#ededed',\n        popover: '#111111',\n        popoverForeground: '#ededed',\n        primary: '#60a5fa',\n        primaryForeground: '#0a0a0a',\n        secondary: '#1e293b',\n        secondaryForeground: '#ededed',\n        muted: '#1e293b',\n        mutedForeground: '#94a3b8',\n        accent: '#1e293b',\n        accentForeground: '#ededed',\n        destructive: '#dc2626',\n        destructiveForeground: '#ededed',\n        border: '#27272a',\n        input: '#27272a',\n        ring: '#60a5fa',\n        // Enhanced semantic colors\n        textPrimary: '#ededed',\n        textSecondary: '#a1a1aa',\n        textTertiary: '#71717a',\n        textInverse: '#0a0a0a',\n        surfacePrimary: '#0a0a0a',\n        surfaceSecondary: '#111111',\n        surfaceTertiary: '#1a1a1a',\n        surfaceHover: '#1e1e1e',\n        surfaceActive: '#27272a',\n        success: '#22c55e',\n        successForeground: '#0a0a0a',\n        warning: '#f59e0b',\n        warningForeground: '#0a0a0a',\n        info: '#60a5fa',\n        infoForeground: '#0a0a0a',\n        // Chart colors - adjusted for dark theme\n        chart1: '#60a5fa',\n        chart2: '#34d399',\n        chart3: '#fbbf24',\n        chart4: '#f87171',\n        chart5: '#a78bfa',\n        chart6: '#22d3ee',\n        chart7: '#a3e635',\n        chart8: '#fb923c',\n        // Status colors - adjusted for dark theme\n        statusMeeting: '#60a5fa',\n        statusDeadline: '#f87171',\n        statusEvent: '#34d399',\n        statusActive: '#34d399',\n        statusPending: '#fbbf24',\n        statusInactive: '#9ca3af'\n    }\n};\n/**\n * Utility function to get the current theme mode\n */ function getThemeMode() {\n    if (false) {}\n    const stored = localStorage.getItem('theme-mode');\n    if (stored === 'light' || stored === 'dark') return stored;\n    return 'system';\n}\n/**\n * Utility function to set the theme mode\n */ function setThemeMode(mode) {\n    if (false) {}\n    if (mode === 'system') {\n        localStorage.removeItem('theme-mode');\n    } else {\n        localStorage.setItem('theme-mode', mode);\n    }\n}\n/**\n * Utility function to toggle between light and dark modes\n */ function toggleTheme() {\n    const current = getThemeMode();\n    const next = current === 'light' ? 'dark' : 'light';\n    setThemeMode(next);\n}\n/**\n * Hook to detect system theme preference\n */ function useSystemTheme() {\n    if (false) {}\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n}\n/**\n * Apply theme to document element\n */ function applyTheme(mode) {\n    if (false) {}\n    const root = document.documentElement;\n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n    if (mode === 'system') {\n        // Use system preference\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        root.classList.add(systemTheme);\n    } else {\n        // Use explicit theme\n        root.classList.add(mode);\n    }\n}\n/**\n * Initialize theme on app load\n */ function initializeTheme() {\n    if (false) {}\n    const stored = localStorage.getItem('theme-mode');\n    const mode = stored || 'system';\n    applyTheme(mode);\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = ()=>{\n        if (getThemeMode() === 'system') {\n            applyTheme('system');\n        }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return ()=>mediaQuery.removeEventListener('change', handleChange);\n}\n/**\n * Utility to get contrast-safe text color for a given background\n */ function getContrastTextColor(backgroundColor) {\n    let theme = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'light';\n    // Simple heuristic - in a real app you might want to use a proper contrast calculation\n    const colors = themeColors[theme];\n    // For dark backgrounds, use light text\n    if (backgroundColor.includes('dark') || backgroundColor.includes('black') || backgroundColor === colors.primary || backgroundColor === colors.destructive) {\n        return colors.textInverse;\n    }\n    // For light backgrounds, use dark text\n    return colors.textPrimary;\n}\n/**\n * Status color mappings for consistent UI\n */ const statusColors = {\n    active: {\n        light: 'bg-green-500/10 text-green-600',\n        dark: 'bg-green-500/10 text-green-400'\n    },\n    inactive: {\n        light: 'bg-muted text-muted-foreground',\n        dark: 'bg-muted text-muted-foreground'\n    },\n    pending: {\n        light: 'bg-yellow-500/10 text-yellow-600',\n        dark: 'bg-yellow-500/10 text-yellow-400'\n    },\n    approved: {\n        light: 'bg-green-500/10 text-green-600',\n        dark: 'bg-green-500/10 text-green-400'\n    },\n    rejected: {\n        light: 'bg-red-500/10 text-red-600',\n        dark: 'bg-red-500/10 text-red-400'\n    },\n    draft: {\n        light: 'bg-muted text-muted-foreground',\n        dark: 'bg-muted text-muted-foreground'\n    }\n};\n/**\n * Get status color classes for current theme\n */ function getStatusColor(status) {\n    let theme = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'light';\n    return statusColors[status][theme];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/theme.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);