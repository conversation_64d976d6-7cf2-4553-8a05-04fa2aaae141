/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/layout.tsx */ \"(rsc)/./src/app/auth/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Blb3BsZU5lc3QlNUMlNUNwZW9wbGVuZXN0LXVpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDUGVvcGxlTmVzdCU1QyU1Q3Blb3BsZW5lc3QtdWklNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Blb3BsZU5lc3QlNUMlNUNwZW9wbGVuZXN0LXVpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQWdJO0FBQ2hJO0FBQ0EsME9BQW1JO0FBQ25JO0FBQ0EsME9BQW1JO0FBQ25JO0FBQ0Esb1JBQXlKO0FBQ3pKO0FBQ0Esd09BQWtJO0FBQ2xJO0FBQ0EsNFBBQTZJO0FBQzdJO0FBQ0Esa1FBQWdKO0FBQ2hKO0FBQ0Esc1FBQWlKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQZW9wbGVOZXN0XFxcXHBlb3BsZW5lc3QtdWlcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUGVvcGxlTmVzdFxcXFxwZW9wbGVuZXN0LXVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQZW9wbGVOZXN0XFxcXHBlb3BsZW5lc3QtdWlcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUGVvcGxlTmVzdFxcXFxwZW9wbGVuZXN0LXVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUGVvcGxlTmVzdFxcXFxwZW9wbGVuZXN0LXVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQZW9wbGVOZXN0XFxcXHBlb3BsZW5lc3QtdWlcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxQZW9wbGVOZXN0XFxwZW9wbGVuZXN0LXVpXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst metadata = {\n    title: \"Authentication - PeopleNest HRMS\",\n    description: \"Sign in to your PeopleNest HRMS account\",\n    robots: {\n        index: false,\n        follow: false\n    }\n};\nfunction AuthLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"absolute bottom-4 left-0 right-0 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-muted-foreground\",\n                    children: \"\\xa9 2024 PeopleNest. All rights reserved. | Privacy Policy | Terms of Service\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0c31b8ade169\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcUGVvcGxlTmVzdFxccGVvcGxlbmVzdC11aVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMGMzMWI4YWRlMTY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"PeopleNest - Enterprise HRMS Platform\",\n    description: \"Modern, AI-powered Human Resource Management System for enterprise organizations\",\n    keywords: [\n        \"HRMS\",\n        \"HR\",\n        \"Human Resources\",\n        \"Employee Management\",\n        \"Payroll\",\n        \"Performance\"\n    ],\n    manifest: \"/manifest.json\",\n    themeColor: \"#3b82f6\",\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1,\n        userScalable: false,\n        viewportFit: \"cover\"\n    },\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"PeopleNest HRMS\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    other: {\n        \"mobile-web-app-capable\": \"yes\",\n        \"apple-mobile-web-app-capable\": \"yes\",\n        \"apple-mobile-web-app-status-bar-style\": \"default\",\n        \"apple-mobile-web-app-title\": \"PeopleNest\",\n        \"application-name\": \"PeopleNest HRMS\",\n        \"msapplication-TileColor\": \"#3b82f6\",\n        \"msapplication-config\": \"/browserconfig.xml\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"light\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"PeopleNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"PeopleNest HRMS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-sans antialiased touch-manipulation`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'system';\n                  var root = document.documentElement;\n\n                  root.classList.remove('light', 'dark');\n\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.classList.add(systemTheme);\n                  } else {\n                    root.classList.add(theme);\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.classList.add('light');\n                }\n              })();\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(ssr)/./src/components/providers/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// Form validation schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Password is required\").min(6, \"Password must be at least 6 characters\"),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean().default(false)\n});\nfunction LoginPage() {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginSuccess, setLoginSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, isAuthenticated, isLoading: authLoading } = (0,_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__.useAuth)();\n    const { register, handleSubmit, formState: { errors, isSubmitting }, watch, trigger } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: \"\",\n            password: \"\",\n            rememberMe: false\n        },\n        mode: \"onChange\"\n    });\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (isAuthenticated && !authLoading) {\n                router.push(\"/dashboard\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"LoginPage.useEffect.handleKeyDown\": (event)=>{\n                    // Alt + D to fill demo credentials in development\n                    if ( true && event.altKey && event.key === 'd') {\n                        event.preventDefault();\n                        const emailInput = document.querySelector('input[name=\"email\"]');\n                        const passwordInput = document.querySelector('input[name=\"password\"]');\n                        if (emailInput && passwordInput) {\n                            emailInput.value = '<EMAIL>';\n                            passwordInput.value = 'password123';\n                            emailInput.dispatchEvent(new Event('input', {\n                                bubbles: true\n                            }));\n                            passwordInput.dispatchEvent(new Event('input', {\n                                bubbles: true\n                            }));\n                        }\n                    }\n                }\n            }[\"LoginPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"LoginPage.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"LoginPage.useEffect\"];\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const onSubmit = async (data)=>{\n        try {\n            setLoginError(null);\n            setLoginSuccess(false);\n            const success = await login(data.email, data.password);\n            if (success) {\n                setLoginSuccess(true);\n                // Small delay to show success message before redirect\n                setTimeout(()=>{\n                    router.push(\"/dashboard\");\n                }, 1000);\n            } else {\n                setLoginError(\"Invalid email or password. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            setLoginError(\"An unexpected error occurred. Please try again.\");\n        }\n    };\n    // Watch form values for real-time validation feedback\n    const watchedEmail = watch(\"email\");\n    const watchedPassword = watch(\"password\");\n    // Show loading state during auth check\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            initial: {\n                                scale: 0.8\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            transition: {\n                                delay: 0.2,\n                                duration: 0.3\n                            },\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-foreground mb-2\",\n                            children: \"PeopleNest\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Enterprise HRMS Platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2 mt-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"SOC 2 Compliant\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: \"Enterprise Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            className: \"space-y-1 pb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-2xl font-semibold text-center\",\n                                    children: \"Welcome back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    className: \"text-center\",\n                                    children: \"Sign in to your account to continue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                loginSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                        className: \"border-green-200 bg-green-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                className: \"text-green-800\",\n                                                children: \"Login successful! Redirecting to dashboard...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                        variant: \"destructive\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                children: loginError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit(onSubmit),\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        ...register(\"email\"),\n                                                        type: \"email\",\n                                                        placeholder: \"Enter your email\",\n                                                        label: \"Email Address\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        error: errors.email?.message,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        ...register(\"password\"),\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        placeholder: \"Enter your password\",\n                                                        label: \"Password\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                            disabled: isSubmitting,\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 27\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        error: errors.password?.message,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ...register(\"rememberMe\"),\n                                                            type: \"checkbox\",\n                                                            className: \"rounded border-gray-300 text-primary focus:ring-primary disabled:opacity-50\",\n                                                            disabled: isSubmitting\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Remember me\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed\",\n                                                    onClick: (e)=>e.preventDefault(),\n                                                    children: \"Forgot password? (Coming Soon)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full h-11 text-base font-medium\",\n                                            loading: isSubmitting,\n                                            disabled: isSubmitting || loginSuccess,\n                                            children: isSubmitting ? \"Signing in...\" : loginSuccess ? \"Success!\" : \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: \"Demo Credentials\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"h-6 px-2 text-xs\",\n                                                    onClick: ()=>{\n                                                        const form = document.querySelector('form');\n                                                        const emailInput = form?.querySelector('input[name=\"email\"]');\n                                                        const passwordInput = form?.querySelector('input[name=\"password\"]');\n                                                        if (emailInput && passwordInput) {\n                                                            emailInput.value = '<EMAIL>';\n                                                            passwordInput.value = 'password123';\n                                                            emailInput.dispatchEvent(new Event('input', {\n                                                                bubbles: true\n                                                            }));\n                                                            passwordInput.dispatchEvent(new Event('input', {\n                                                                bubbles: true\n                                                            }));\n                                                        }\n                                                    },\n                                                    disabled: isSubmitting,\n                                                    children: \"Quick Fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-xs text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Email:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" <EMAIL>\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Password:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" Any password (6+ characters)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-600 mt-2\",\n                                                    children: \"Development mode - any valid email/password combination will work\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-500 mt-1\",\n                                                    children: \"Tip: Press Alt + D to quick fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed\",\n                                                onClick: (e)=>e.preventDefault(),\n                                                children: \"Contact your administrator\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.8,\n                        duration: 0.3\n                    },\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: \"Protected by enterprise-grade security and encryption\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        isAuthenticated: false,\n        isLoading: true,\n        token: null\n    });\n    // Initialize auth state from storage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        const storedToken = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                        const storedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredUser)();\n                        if (storedToken && storedUser) {\n                            // Verify token is still valid by calling /me endpoint\n                            const isValid = await verifyToken(storedToken);\n                            if (isValid) {\n                                setAuthState({\n                                    user: storedUser,\n                                    isAuthenticated: true,\n                                    isLoading: false,\n                                    token: storedToken\n                                });\n                            } else {\n                                // Token is invalid, clear storage\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.removeStoredToken)();\n                                setAuthState({\n                                    user: null,\n                                    isAuthenticated: false,\n                                    isLoading: false,\n                                    token: null\n                                });\n                            }\n                        } else {\n                            // For development, use mock user if no auth found\n                            if (true) {\n                                setAuthState({\n                                    user: _lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER,\n                                    isAuthenticated: true,\n                                    isLoading: false,\n                                    token: 'mock-token'\n                                });\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER);\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)('mock-token');\n                            } else {}\n                        }\n                    } catch (error) {\n                        console.error('Failed to initialize auth:', error);\n                        setAuthState({\n                            user: null,\n                            isAuthenticated: false,\n                            isLoading: false,\n                            token: null\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const verifyToken = async (token)=>{\n        try {\n            // Skip verification in development mode\n            if (true) {\n                return true;\n            }\n            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.ME}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.error('Token verification failed:', error);\n            return false;\n        }\n    };\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[login]\": async (email, password)=>{\n            try {\n                setAuthState({\n                    \"AuthProvider.useCallback[login]\": (prev)=>({\n                            ...prev,\n                            isLoading: true\n                        })\n                }[\"AuthProvider.useCallback[login]\"]);\n                // In development mode, simulate login\n                if (true) {\n                    // Simulate API delay\n                    await new Promise({\n                        \"AuthProvider.useCallback[login]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"AuthProvider.useCallback[login]\"]);\n                    const mockToken = 'mock-jwt-token';\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(mockToken);\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER);\n                    setAuthState({\n                        user: _lib_auth__WEBPACK_IMPORTED_MODULE_2__.MOCK_USER,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        token: mockToken\n                    });\n                    return true;\n                }\n                const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.LOGIN}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    const { token, user } = data;\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(token);\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n                    setAuthState({\n                        user,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        token\n                    });\n                    return true;\n                } else {\n                    setAuthState({\n                        \"AuthProvider.useCallback[login]\": (prev)=>({\n                                ...prev,\n                                isLoading: false\n                            })\n                    }[\"AuthProvider.useCallback[login]\"]);\n                    return false;\n                }\n            } catch (error) {\n                console.error('Login failed:', error);\n                setAuthState({\n                    \"AuthProvider.useCallback[login]\": (prev)=>({\n                            ...prev,\n                            isLoading: false\n                        })\n                }[\"AuthProvider.useCallback[login]\"]);\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[login]\"], []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.removeStoredToken)();\n            setAuthState({\n                user: null,\n                isAuthenticated: false,\n                isLoading: false,\n                token: null\n            });\n            // Redirect to login page\n            if (false) {}\n        }\n    }[\"AuthProvider.useCallback[logout]\"], []);\n    const refreshToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshToken]\": async ()=>{\n            try {\n                const currentToken = authState.token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                if (!currentToken) {\n                    return false;\n                }\n                // Skip refresh in development mode\n                if (true) {\n                    return true;\n                }\n                const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.REFRESH}`, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${currentToken}`,\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    const { token, user } = data;\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(token);\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n                    setAuthState({\n                        \"AuthProvider.useCallback[refreshToken]\": (prev)=>({\n                                ...prev,\n                                user,\n                                token\n                            })\n                    }[\"AuthProvider.useCallback[refreshToken]\"]);\n                    return true;\n                } else {\n                    logout();\n                    return false;\n                }\n            } catch (error) {\n                console.error('Token refresh failed:', error);\n                logout();\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshToken]\"], [\n        authState.token,\n        logout\n    ]);\n    const updateUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updateUser]\": (user)=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n            setAuthState({\n                \"AuthProvider.useCallback[updateUser]\": (prev)=>({\n                        ...prev,\n                        user\n                    })\n            }[\"AuthProvider.useCallback[updateUser]\"]);\n        }\n    }[\"AuthProvider.useCallback[updateUser]\"], []);\n    const value = {\n        ...authState,\n        login,\n        logout,\n        refreshToken,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n            warning: \"border-yellow-500/50 text-yellow-900 bg-yellow-50 dark:border-yellow-500 dark:text-yellow-100 dark:bg-yellow-950 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400\",\n            success: \"border-green-500/50 text-green-900 bg-green-50 dark:border-green-500 dark:text-green-100 dark:bg-green-950 [&>svg]:text-green-600 dark:[&>svg]:text-green-400\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400\",\n            warning: \"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400\",\n            info: \"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400\",\n            // HRMS specific variants\n            active: \"border-transparent bg-green-500/10 text-green-600 dark:text-green-400\",\n            inactive: \"border-transparent bg-muted text-muted-foreground\",\n            pending: \"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400\",\n            approved: \"border-transparent bg-green-500/10 text-green-600 dark:text-green-400\",\n            rejected: \"border-transparent bg-red-500/10 text-red-600 dark:text-red-400\",\n            draft: \"border-transparent bg-muted text-muted-foreground\"\n        },\n        size: {\n            default: \"px-2.5 py-0.5 text-xs\",\n            sm: \"px-2 py-0.5 text-xs\",\n            lg: \"px-3 py-1 text-sm\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Badge({ className, variant, size, icon, removable, onRemove, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant,\n            size\n        }), className),\n        ...props,\n        children: [\n            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                lineNumber: 70,\n                columnNumber: 16\n            }, this),\n            children,\n            removable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10\",\n                onClick: onRemove,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-3 w-3\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            success: \"bg-green-600 text-white hover:bg-green-700\",\n            warning: \"bg-yellow-600 text-white hover:bg-yellow-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 70,\n                columnNumber: 11\n            }, undefined),\n            !loading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 92,\n                columnNumber: 11\n            }, undefined),\n            children,\n            !loading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 96,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 63,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", {\n    variants: {\n        variant: {\n            default: \"border-border\",\n            elevated: \"shadow-md\",\n            outlined: \"border-2\",\n            ghost: \"border-transparent shadow-none\"\n        },\n        padding: {\n            none: \"\",\n            sm: \"p-4\",\n            default: \"p-6\",\n            lg: \"p-8\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        padding: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, padding, hover = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(cardVariants({\n            variant,\n            padding\n        }), hover && \"transition-shadow hover:shadow-md\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input),\n/* harmony export */   inputVariants: () => (/* binding */ inputVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n\n\n\n\nconst inputVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"\",\n            error: \"border-red-500 focus-visible:ring-red-500\",\n            success: \"border-green-500 focus-visible:ring-green-500\"\n        },\n        size: {\n            default: \"h-10\",\n            sm: \"h-9 px-2 text-xs\",\n            lg: \"h-11 px-4\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, variant, size, leftIcon, rightIcon, error, label, helperText, id, ...props }, ref)=>{\n    const inputId = id || react__WEBPACK_IMPORTED_MODULE_1__.useId();\n    const hasError = !!error;\n    const finalVariant = hasError ? \"error\" : variant;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: inputId,\n                className: \"block text-sm font-medium text-foreground mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                lineNumber: 58,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\",\n                        children: leftIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: type,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(inputVariants({\n                            variant: finalVariant,\n                            size,\n                            className\n                        }), leftIcon && \"pl-10\", rightIcon && \"pr-10\"),\n                        ref: ref,\n                        id: inputId,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\",\n                        children: rightIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined),\n            (error || helperText) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-1 text-xs\", hasError ? \"text-destructive\" : \"text-muted-foreground\"),\n                children: error || helperText\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                lineNumber: 89,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 56,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ENDPOINTS: () => (/* binding */ AUTH_ENDPOINTS),\n/* harmony export */   MOCK_USER: () => (/* binding */ MOCK_USER),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   TOKEN_KEY: () => (/* binding */ TOKEN_KEY),\n/* harmony export */   USER_KEY: () => (/* binding */ USER_KEY),\n/* harmony export */   canAccessAdminModule: () => (/* binding */ canAccessAdminModule),\n/* harmony export */   canAccessHRModule: () => (/* binding */ canAccessHRModule),\n/* harmony export */   canAccessOrganizationModule: () => (/* binding */ canAccessOrganizationModule),\n/* harmony export */   canManageUsers: () => (/* binding */ canManageUsers),\n/* harmony export */   canReadAllEmployees: () => (/* binding */ canReadAllEmployees),\n/* harmony export */   canReadDepartments: () => (/* binding */ canReadDepartments),\n/* harmony export */   canReadPositions: () => (/* binding */ canReadPositions),\n/* harmony export */   canWriteAllEmployees: () => (/* binding */ canWriteAllEmployees),\n/* harmony export */   canWriteDepartments: () => (/* binding */ canWriteDepartments),\n/* harmony export */   canWritePositions: () => (/* binding */ canWritePositions),\n/* harmony export */   getStoredToken: () => (/* binding */ getStoredToken),\n/* harmony export */   getStoredUser: () => (/* binding */ getStoredUser),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasAnyRole: () => (/* binding */ hasAnyRole),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isRoleAtLeast: () => (/* binding */ isRoleAtLeast),\n/* harmony export */   removeStoredToken: () => (/* binding */ removeStoredToken),\n/* harmony export */   setStoredToken: () => (/* binding */ setStoredToken),\n/* harmony export */   setStoredUser: () => (/* binding */ setStoredUser)\n/* harmony export */ });\n// Authentication and authorization utilities\n// Permission hierarchy - higher roles inherit lower role permissions\nconst ROLE_PERMISSIONS = {\n    employee: [\n        'employee_read_own',\n        'employee_update_own'\n    ],\n    manager: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team'\n    ],\n    hr: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read'\n    ],\n    hr_admin: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read',\n        'hr_admin',\n        'department_write',\n        'position_write',\n        'user_management'\n    ],\n    super_admin: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read',\n        'hr_admin',\n        'department_write',\n        'position_write',\n        'user_management',\n        'super_admin',\n        'system_admin'\n    ]\n};\n// Token management\nconst TOKEN_KEY = 'peoplenest_auth_token';\nconst USER_KEY = 'peoplenest_user';\nfunction getStoredToken() {\n    if (true) return null;\n    return localStorage.getItem(TOKEN_KEY);\n}\nfunction setStoredToken(token) {\n    if (true) return;\n    localStorage.setItem(TOKEN_KEY, token);\n}\nfunction removeStoredToken() {\n    if (true) return;\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(USER_KEY);\n}\nfunction getStoredUser() {\n    if (true) return null;\n    const userStr = localStorage.getItem(USER_KEY);\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch  {\n        return null;\n    }\n}\nfunction setStoredUser(user) {\n    if (true) return;\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\n}\n// Permission checking utilities\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasRole(user, role) {\n    if (!user) return false;\n    return user.role === role;\n}\nfunction hasAnyRole(user, roles) {\n    if (!user) return false;\n    return roles.includes(user.role);\n}\nfunction isRoleAtLeast(user, minRole) {\n    if (!user) return false;\n    const roleHierarchy = [\n        'employee',\n        'manager',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ];\n    const userRoleIndex = roleHierarchy.indexOf(user.role);\n    const minRoleIndex = roleHierarchy.indexOf(minRole);\n    return userRoleIndex >= minRoleIndex;\n}\n// Department and organization permissions\nfunction canReadDepartments(user) {\n    return hasAnyPermission(user, [\n        'department_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWriteDepartments(user) {\n    return hasAnyPermission(user, [\n        'department_write',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canReadPositions(user) {\n    return hasAnyPermission(user, [\n        'position_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWritePositions(user) {\n    return hasAnyPermission(user, [\n        'position_write',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canReadAllEmployees(user) {\n    return hasAnyPermission(user, [\n        'employee_read_all',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWriteAllEmployees(user) {\n    return hasAnyPermission(user, [\n        'employee_update_all',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canManageUsers(user) {\n    return hasAnyPermission(user, [\n        'user_management',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\n// Navigation permissions\nfunction canAccessOrganizationModule(user) {\n    return hasAnyPermission(user, [\n        'department_read',\n        'position_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canAccessHRModule(user) {\n    return hasAnyPermission(user, [\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canAccessAdminModule(user) {\n    return hasAnyPermission(user, [\n        'hr_admin',\n        'super_admin'\n    ]);\n}\n// Mock user for development (remove in production)\nconst MOCK_USER = {\n    id: '1',\n    employeeId: 'EMP001',\n    email: '<EMAIL>',\n    firstName: 'Admin',\n    lastName: 'User',\n    role: 'hr_admin',\n    permissions: ROLE_PERMISSIONS.hr_admin,\n    departmentId: '1',\n    managerId: undefined\n};\n// API endpoints\nconst AUTH_ENDPOINTS = {\n    LOGIN: '/api/auth/login',\n    LOGOUT: '/api/auth/logout',\n    REFRESH: '/api/auth/refresh',\n    ME: '/api/auth/me'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        month: \"long\",\n        day: \"numeric\",\n        year: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\nfunction formatPercentage(value) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"percent\",\n        minimumFractionDigits: 1,\n        maximumFractionDigits: 1\n    }).format(value / 100);\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((n)=>n[0]).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w ]+/g, \"\").replace(/ +/g, \"-\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@radix-ui","vendor-chunks/motion-dom","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/zod","vendor-chunks/react-hook-form","vendor-chunks/@hookform"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();