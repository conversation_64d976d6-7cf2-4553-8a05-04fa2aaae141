import express from 'express'
import { query, param, body, validationResult } from 'express-validator'
import { authMiddleware } from '../middleware/auth'
import { 
  requireResourcePermission,
  attachUserPermissions
} from '../middleware/enhancedRBAC'
import { auditMiddleware } from '../middleware/audit'
import { auditService } from '../services/auditService'
import { logger } from '../utils/logger'

const router = express.Router()

// Apply authentication and permission attachment to all routes
router.use(authMiddleware)
router.use(attachUserPermissions)

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * GET /audit/events
 * Get audit events (Admin only)
 */
router.get('/events',
  requireResourcePermission('audit', 'read', { scope: 'all' }),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('userId').optional().isUUID().withMessage('User ID must be a valid UUID'),
    query('eventType').optional().isString().withMessage('Event type must be a string'),
    query('severity').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Invalid severity level'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date')
  ],
  validateRequest,
  auditMiddleware('audit_events_accessed', 'data_access'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page as string) || 1
      const limit = parseInt(req.query.limit as string) || 50
      const offset = (page - 1) * limit

      const filters: any = {}
      if (req.query.userId) filters.userId = req.query.userId
      if (req.query.eventType) filters.eventType = req.query.eventType
      if (req.query.severity) filters.severity = req.query.severity
      if (req.query.startDate) filters.startDate = new Date(req.query.startDate as string)
      if (req.query.endDate) filters.endDate = new Date(req.query.endDate as string)

      // Build dynamic query
      let whereClause = 'WHERE 1=1'
      const queryParams: any[] = []
      let paramIndex = 1

      if (filters.userId) {
        whereClause += ` AND user_id = $${paramIndex++}`
        queryParams.push(filters.userId)
      }
      if (filters.eventType) {
        whereClause += ` AND event_type = $${paramIndex++}`
        queryParams.push(filters.eventType)
      }
      if (filters.severity) {
        whereClause += ` AND severity = $${paramIndex++}`
        queryParams.push(filters.severity)
      }
      if (filters.startDate) {
        whereClause += ` AND timestamp >= $${paramIndex++}`
        queryParams.push(filters.startDate)
      }
      if (filters.endDate) {
        whereClause += ` AND timestamp <= $${paramIndex++}`
        queryParams.push(filters.endDate)
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM audit_events ${whereClause}`
      const countResult = await auditService['db'].query(countQuery, queryParams)
      const total = parseInt(countResult.rows[0].total)

      // Get paginated results
      const dataQuery = `
        SELECT id, user_id, event_type, event_category, resource, action,
               details, ip_address, timestamp, severity, success, risk_score
        FROM audit_events 
        ${whereClause}
        ORDER BY timestamp DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `
      queryParams.push(limit, offset)

      const dataResult = await auditService['db'].query(dataQuery, queryParams)

      res.json({
        success: true,
        data: {
          events: dataResult.rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      })

    } catch (error) {
      logger.error('Failed to get audit events:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to retrieve audit events'
      })
    }
  }
)

/**
 * GET /audit/security-incidents
 * Get security incidents (Admin only)
 */
router.get('/security-incidents',
  requireResourcePermission('security', 'read', { scope: 'all' }),
  [
    query('severity').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Invalid severity level'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  validateRequest,
  auditMiddleware('security_incidents_accessed', 'data_access'),
  async (req, res) => {
    try {
      const severity = req.query.severity as string
      const limit = parseInt(req.query.limit as string) || 100

      const incidents = await auditService.getSecurityIncidents(severity, limit)

      res.json({
        success: true,
        data: {
          incidents,
          total: incidents.length
        }
      })

    } catch (error) {
      logger.error('Failed to get security incidents:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to retrieve security incidents'
      })
    }
  }
)

/**
 * GET /audit/user/:userId/trail
 * Get audit trail for specific user (GDPR compliance)
 */
router.get('/user/:userId/trail',
  requireResourcePermission('users', 'read', { scope: 'all' }),
  [
    param('userId').isUUID().withMessage('User ID must be a valid UUID'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date')
  ],
  validateRequest,
  auditMiddleware('user_audit_trail_accessed', 'data_access'),
  async (req, res) => {
    try {
      const { userId } = req.params
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined

      const auditTrail = await auditService.getUserAuditTrail(userId, startDate, endDate)

      res.json({
        success: true,
        data: {
          userId,
          period: {
            start: startDate,
            end: endDate
          },
          events: auditTrail,
          total: auditTrail.length
        }
      })

    } catch (error) {
      logger.error('Failed to get user audit trail:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to retrieve user audit trail'
      })
    }
  }
)

/**
 * POST /audit/reports/generate
 * Generate compliance report
 */
router.post('/reports/generate',
  requireResourcePermission('reports', 'create', { scope: 'all' }),
  [
    body('reportType').isIn(['gdpr_access', 'data_retention', 'security_incidents', 'user_activity'])
      .withMessage('Invalid report type'),
    body('startDate').isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    body('endDate').isISO8601().withMessage('End date must be valid ISO 8601 date'),
    body('userId').optional().isUUID().withMessage('User ID must be a valid UUID'),
    body('filters').optional().isObject().withMessage('Filters must be an object')
  ],
  validateRequest,
  auditMiddleware('compliance_report_generated', 'administrative_action'),
  async (req, res) => {
    try {
      const { reportType, startDate, endDate, userId, filters } = req.body

      const report = await auditService.generateComplianceReport({
        reportType,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        userId,
        filters
      })

      // Store report in database
      await auditService['db'].query(`
        INSERT INTO compliance_reports (
          report_type, report_name, start_date, end_date, filters,
          report_data, summary, total_records, generated_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        reportType,
        `${reportType}_${new Date().toISOString().split('T')[0]}`,
        startDate,
        endDate,
        JSON.stringify(filters || {}),
        JSON.stringify(report),
        JSON.stringify({ generated: new Date() }),
        report.totalRecords || 0,
        req.user.id
      ])

      res.json({
        success: true,
        message: 'Compliance report generated successfully',
        data: report
      })

    } catch (error) {
      logger.error('Failed to generate compliance report:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to generate compliance report'
      })
    }
  }
)

/**
 * GET /audit/dashboard/summary
 * Get audit dashboard summary
 */
router.get('/dashboard/summary',
  requireResourcePermission('audit', 'read', { scope: 'all' }),
  auditMiddleware('audit_dashboard_accessed', 'data_access'),
  async (req, res) => {
    try {
      // Get audit summary from view
      const auditSummary = await auditService['db'].query(`
        SELECT * FROM audit_summary ORDER BY date DESC LIMIT 30
      `)

      // Get security dashboard from view
      const securityDashboard = await auditService['db'].query(`
        SELECT * FROM security_dashboard ORDER BY hour DESC LIMIT 24
      `)

      // Get recent high-risk events
      const highRiskEvents = await auditService['db'].query(`
        SELECT event_type, severity, risk_score, timestamp, user_id
        FROM audit_events
        WHERE severity IN ('high', 'critical')
        AND timestamp >= NOW() - INTERVAL '24 hours'
        ORDER BY risk_score DESC, timestamp DESC
        LIMIT 10
      `)

      // Get active alerts
      const activeAlerts = await auditService['db'].query(`
        SELECT alert_type, severity, risk_score, triggered_at, status
        FROM activity_monitoring_alerts
        WHERE status = 'open'
        ORDER BY risk_score DESC, triggered_at DESC
        LIMIT 10
      `)

      res.json({
        success: true,
        data: {
          auditSummary: auditSummary.rows,
          securityDashboard: securityDashboard.rows,
          highRiskEvents: highRiskEvents.rows,
          activeAlerts: activeAlerts.rows
        }
      })

    } catch (error) {
      logger.error('Failed to get audit dashboard summary:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to retrieve audit dashboard summary'
      })
    }
  }
)

/**
 * POST /audit/cleanup
 * Cleanup old audit data (Super Admin only)
 */
router.post('/cleanup',
  requireResourcePermission('system', 'admin', { scope: 'all' }),
  [
    body('retentionDays').optional().isInt({ min: 365, max: 3650 })
      .withMessage('Retention days must be between 365 and 3650 (1-10 years)')
  ],
  validateRequest,
  auditMiddleware('audit_cleanup_initiated', 'administrative_action'),
  async (req, res) => {
    try {
      const retentionDays = req.body.retentionDays || 2555 // 7 years default

      await auditService.cleanupAuditData(retentionDays)

      res.json({
        success: true,
        message: 'Audit data cleanup completed successfully',
        data: {
          retentionDays
        }
      })

    } catch (error) {
      logger.error('Failed to cleanup audit data:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to cleanup audit data'
      })
    }
  }
)

export default router
